// =============================================================================
// CARNOW UNIFIED AUTHENTICATION PROVIDER
// =============================================================================
//
// This file implements the unified authentication provider for the CarNow app
// using Riverpod for state management and dependency injection.
//
// Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
//
// Key Features:
// - Comprehensive state management with Riverpod
// - Automatic token refresh and session management
// - Session persistence and restoration on app startup
// - Integration with secure token storage service
// - Production-ready error handling and logging
//
// Author: CarNow Development Team
// =============================================================================

import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:dio/dio.dart';

// import '../utils/unified_logger.dart'; // Removed unused import
import '../models/api_response.dart';
import '../config/backend_config.dart';
import 'auth_models.dart';
import 'auth_interfaces.dart';
import 'enhanced_secure_token_storage.dart';
import 'production_google_auth_service_v6.dart';
import 'auth_initialization_service.dart';
import 'token_storage_migration.dart';

// Removed unused import for WidgetsBinding

part 'unified_auth_provider.g.dart';

// =============================================================================
// UNIFIED AUTHENTICATION PROVIDER
// =============================================================================

/// Authentication initialization phases to prevent race conditions
enum AuthInitializationPhase {
  notStarted,
  initializing,
  validatingTokens,
  restoringSession,
  ready,
  failed,
}

/// Unified authentication provider that manages the complete authentication
/// state and lifecycle for the CarNow application.
///
/// This provider handles:
/// - Authentication state management
/// - Token refresh and session management
/// - Session persistence and restoration
/// - Integration with secure storage and API services
///
/// Architecture compliance: Flutter UI Only → Go API → Supabase Data
@riverpod
class UnifiedAuthProvider extends _$UnifiedAuthProvider {
  // ---------------------------------------------------------------------------
  // Dependencies and Services
  // ---------------------------------------------------------------------------

  late final ITokenStorage _tokenStorage;
  late final Dio _internalDio; // Separate Dio instance for internal auth calls
  late final IGoogleAuthService _googleAuthService;
  late final AuthInitializationService _initializationService;

  Timer? _tokenRefreshTimer;
  AuthInitializationPhase _initializationPhase =
      AuthInitializationPhase.notStarted;
  StreamSubscription? _authStateSubscription;

  // Track initialization result for debugging and monitoring
  AuthInitializationResult? _lastInitializationResult;
  
  // Prevent multiple background initialization calls
  bool _backgroundInitializationRunning = false;

  /// Create a separate Dio instance for internal auth API calls
  /// This avoids circular dependency with SimpleApiClient
  Dio _createInternalDio() {
    final dio = Dio();
    
    // Configure base options
    dio.options.baseUrl = BackendConfig.baseUrl;
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    return dio;
  }

  // ---------------------------------------------------------------------------
  // Provider Initialization
  // ---------------------------------------------------------------------------

  @override
  AuthState build() {
    // Initialize dependencies only once
    _tokenStorage = ref.watch(enhancedSecureTokenStorageProvider);
    _internalDio = _createInternalDio(); // Create separate Dio for internal calls
    _googleAuthService = ref.watch(productionGoogleAuthServiceProviderV6);
    _initializationService = ref.watch(authInitializationServiceProvider);

    // Set up cleanup on provider disposal
    ref.onCancel(() {
      _tokenRefreshTimer?.cancel();
      _authStateSubscription?.cancel();
      _backgroundInitializationRunning = false;
      _initializationPhase = AuthInitializationPhase.notStarted;
    });

    // Only initialize once - prevent repeated initialization
    if (_initializationPhase == AuthInitializationPhase.notStarted && 
        !_backgroundInitializationRunning) {
      _initializationPhase = AuthInitializationPhase.initializing;
      _backgroundInitializationRunning = true;
      
      developer.log(
        'Starting one-time background initialization',
        name: 'UnifiedAuthProvider',
      );
      
      // Start background initialization immediately
      _performBackgroundInitialization();
      
      // Return initial unauthenticated state
      return const AuthState.unauthenticated();
    }
    
    // For all subsequent builds, return current state without re-initialization
    return state;
  }

  // Removed unused _getSynchronousInitialState method

  // Removed _startBackgroundInitialization method - initialization now happens directly in build()

  /// Perform background initialization using AuthInitializationService
  /// This method runs async operations without affecting the provider's synchronous state
  Future<void> _performBackgroundInitialization() async {
    try {
      developer.log(
        'Starting background AuthInitializationService initialization',
        name: 'UnifiedAuthProvider',
      );

      // Perform token storage migration if needed
      try {
        final migration = ref.read(tokenStorageMigrationProvider);
        await migration.migrateIfNeeded();
      } catch (e) {
        developer.log(
          'Token migration failed, continuing with initialization: $e',
          name: 'UnifiedAuthProvider',
        );
      }

      // Use AuthInitializationService for robust initialization
      final result = await _initializationService.initialize();
      _lastInitializationResult = result;

      // Handle initialization result
      await result.when(
        success: (initialState, initializationTime, configuration, metadata) async {
          developer.log(
            'Background AuthInitializationService succeeded in ${initializationTime.inMilliseconds}ms',
            name: 'UnifiedAuthProvider',
          );

          // Update state only if it's different from current state
          if (state != initialState) {
            state = initialState;
          }
          _initializationPhase = AuthInitializationPhase.ready;

          // Start token refresh timer if authenticated
          if (initialState is AuthStateAuthenticated) {
            _startTokenRefreshTimer();
          }
        },
        failure: (error, errorType, canRetry, fallbackState, details) async {
          developer.log(
            'Background AuthInitializationService failed: $error (canRetry: $canRetry)',
            name: 'UnifiedAuthProvider',
          );

          // Handle failure with proper error boundaries
          await _handleInitializationFailure(
            error,
            errorType,
            canRetry,
            fallbackState,
            details,
          );
        },
        timeout: (timeoutDuration, fallbackState, shouldRetry) async {
          developer.log(
            'Background AuthInitializationService timed out after ${timeoutDuration.inSeconds}s',
            name: 'UnifiedAuthProvider',
          );

          // Handle timeout with proper fallback
          await _handleInitializationTimeout(
            timeoutDuration,
            fallbackState,
            shouldRetry,
          );
        },
      );
    } catch (error, stackTrace) {
      developer.log(
        'Background initialization failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      // Handle unexpected errors with proper error boundaries
      await _handleUnexpectedInitializationError(error, stackTrace);
    } finally {
      // Mark background initialization as completed
      _backgroundInitializationRunning = false;
    }
  }



  /// Fetch user data from backend during session restoration
  /// جلب بيانات المستخدم من الخادم أثناء استعادة الجلسة
  Future<void> _fetchUserDataFromBackend(String accessToken) async {
    try {
      developer.log(
        'Fetching user data from backend',
        name: 'UnifiedAuthProvider',
      );

      // Use internal Dio to fetch user profile (avoids circular dependency)
      final response = await _internalDio.get(
        '/api/v1/auth/profile',
        options: Options(
          headers: {
            'Authorization': 'Bearer $accessToken',
          },
        ),
      );

      if (response.statusCode == 200 &&
          response.data != null &&
          response.data['user'] != null) {
        final userData = Map<String, dynamic>.from(response.data['user']);
        final user = User.fromJson(userData);

        // Store user data for future quick restoration
        await _tokenStorage.storeSessionData({'user': userData});

        state = AuthState.authenticated(user: user, token: accessToken);

        // Start token refresh timer
        _startTokenRefreshTimer();

        // Mark initialization as complete
        _initializationPhase = AuthInitializationPhase.ready;

        developer.log(
          'Session restored from backend for user: ${user.email}',
          name: 'UnifiedAuthProvider',
        );
      } else {
        throw Exception('Invalid user data received from backend: ${response.statusMessage}');
      }
    } catch (error) {
      developer.log(
        'Failed to fetch user data from backend: $error',
        name: 'UnifiedAuthProvider',
        level: 1000,
      );

      // If backend fetch fails, try token refresh
      await _attemptTokenRefresh();
    }
  }

  /// Attempt token refresh during session restoration
  /// محاولة تحديث الرمز المميز أثناء استعادة الجلسة
  Future<void> _attemptTokenRefresh() async {
    try {
      developer.log(
        'Attempting token refresh during session restoration',
        name: 'UnifiedAuthProvider',
      );

      final refreshToken = await _tokenStorage.getRefreshToken();
      if (refreshToken == null) {
        throw Exception('No refresh token available');
      }

      // Use internal Dio for token refresh (avoids circular dependency)
      final response = await _internalDio.post(
        '/api/v1/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200 &&
          response.data != null &&
          response.data['success'] == true) {
        final accessToken = response.data['access_token'] as String;
        final newRefreshToken = response.data['refresh_token'] as String;
        final expiresIn = response.data['expires_in'] as int;

        // Store new tokens
        await _tokenStorage.storeToken(
          accessToken,
          expiryDate: DateTime.now().add(Duration(seconds: expiresIn)),
        );
        await _tokenStorage.storeRefreshToken(
          newRefreshToken,
          expiryDate: DateTime.now().add(const Duration(days: 30)),
        );

        // Set authenticated state directly to avoid infinite loop
        // Try to get user data from storage first
        final storedUserData = await _tokenStorage.getSessionData();
        if (storedUserData.isNotEmpty && storedUserData['user'] != null) {
          final user = User.fromJson(
            storedUserData['user'] as Map<String, dynamic>,
          );
          state = AuthState.authenticated(user: user, token: accessToken);
          _startTokenRefreshTimer();

          // Mark initialization as complete
          _initializationPhase = AuthInitializationPhase.ready;

          developer.log(
            'Token refresh successful - session restored for user: ${user.email}',
            name: 'UnifiedAuthProvider',
          );
        } else {
          // Fetch user data from backend if not in storage
          await _fetchUserDataFromBackend(accessToken);
        }
      } else {
        throw Exception(
          'Token refresh failed - server returned success: false',
        );
      }
    } catch (error) {
      developer.log(
        'Token refresh failed during session restoration: $error',
        name: 'UnifiedAuthProvider',
        level: 1000,
      );

      // If token refresh fails, clear data and set unauthenticated
      await _handleSessionRestorationFailure(error);
    }
  }

  /// Handle initialization failure with proper error boundaries
  /// معالجة فشل التهيئة مع حدود الأخطاء المناسبة
  Future<void> _handleInitializationFailure(
    String error,
    AuthErrorType errorType,
    bool canRetry,
    AuthState? fallbackState,
    Map<String, dynamic> details,
  ) async {
    try {
      developer.log(
        'Handling initialization failure: $error',
        name: 'UnifiedAuthProvider',
        level: 1000,
      );

      // Handle failure based on whether retry is possible
      if (canRetry && _initializationService.failureCount < 3) {
        // Schedule retry with exponential backoff
        final retryDelay = Duration(
          seconds: (2 * _initializationService.failureCount).clamp(1, 10),
        );

        developer.log(
          'Scheduling initialization retry in ${retryDelay.inSeconds}s',
          name: 'UnifiedAuthProvider',
        );

        // Use Future.delayed instead of Timer for better error handling
        Future.delayed(retryDelay, () {
          if (_initializationPhase != AuthInitializationPhase.failed) {
            _initializationPhase = AuthInitializationPhase.notStarted;
            // Retry by resetting initialization phase and calling _performBackgroundInitialization directly
            _backgroundInitializationRunning = true;
            _performBackgroundInitialization();
          }
        });
      } else {
        // Use fallback state if available, otherwise set error state
        if (fallbackState != null) {
          state = fallbackState;
        } else {
          state = AuthState.error(message: error, errorType: errorType);
        }
        _initializationPhase = AuthInitializationPhase.failed;
      }
    } catch (handlingError, stackTrace) {
      developer.log(
        'Error handling initialization failure: $handlingError',
        name: 'UnifiedAuthProvider',
        error: handlingError,
        stackTrace: stackTrace,
      );

      // Fallback to basic error state
      state = AuthState.error(
        message: 'فشل في معالجة خطأ التهيئة',
        errorType: AuthErrorType.unknown,
      );
      _initializationPhase = AuthInitializationPhase.failed;
    }
  }

  /// Handle initialization timeout with proper fallback
  /// معالجة انتهاء مهلة التهيئة مع الاحتياطي المناسب
  Future<void> _handleInitializationTimeout(
    Duration timeoutDuration,
    AuthState fallbackState,
    bool shouldRetry,
  ) async {
    try {
      developer.log(
        'Handling initialization timeout after ${timeoutDuration.inSeconds}s',
        name: 'UnifiedAuthProvider',
      );

      // Use fallback state
      state = fallbackState;
      _initializationPhase = AuthInitializationPhase.ready;

      // Schedule retry if recommended
      if (shouldRetry) {
        Future.delayed(const Duration(seconds: 5), () {
          if (_initializationPhase == AuthInitializationPhase.ready) {
            _initializationPhase = AuthInitializationPhase.notStarted;
            // Retry by resetting initialization phase and calling _performBackgroundInitialization directly
            _backgroundInitializationRunning = true;
            _performBackgroundInitialization();
          }
        });
      }
    } catch (handlingError, stackTrace) {
      developer.log(
        'Error handling initialization timeout: $handlingError',
        name: 'UnifiedAuthProvider',
        error: handlingError,
        stackTrace: stackTrace,
      );

      // Fallback to basic unauthenticated state
      state = const AuthState.unauthenticated(
        reason: 'انتهت مهلة تهيئة نظام المصادقة',
      );
      _initializationPhase = AuthInitializationPhase.failed;
    }
  }

  /// Handle unexpected initialization errors with proper error boundaries
  /// معالجة أخطاء التهيئة غير المتوقعة مع حدود الأخطاء المناسبة
  Future<void> _handleUnexpectedInitializationError(
    dynamic error,
    StackTrace stackTrace,
  ) async {
    try {
      developer.log(
        'Handling unexpected initialization error: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      // Set error state and mark as failed
      state = AuthState.error(
        message: 'فشل في تهيئة نظام المصادقة، يرجى إعادة تشغيل التطبيق',
        errorType: AuthErrorType.unknown,
      );
      _initializationPhase = AuthInitializationPhase.failed;
    } catch (handlingError) {
      developer.log(
        'Critical error in error handling: $handlingError',
        name: 'UnifiedAuthProvider',
        error: handlingError,
      );

      // Last resort fallback
      state = const AuthState.unauthenticated(
        reason: 'خطأ حرج في نظام المصادقة',
      );
      _initializationPhase = AuthInitializationPhase.failed;
    }
  }

  /// Handle session restoration failure
  /// معالجة فشل استعادة الجلسة
  Future<void> _handleSessionRestorationFailure(dynamic error) async {
    try {
      developer.log(
        'Handling session restoration failure: $error',
        name: 'UnifiedAuthProvider',
        level: 900,
      );

      // Clear potentially corrupted data
      await _tokenStorage.clearAllData();

      // Cancel any running timers
      _cancelTokenRefreshTimer();

      // Set unauthenticated state with appropriate message
      String reason = 'فشل في استعادة الجلسة السابقة';

      if (error.toString().contains('network') ||
          error.toString().contains('connection')) {
        reason = 'خطأ في الاتصال بالشبكة - يرجى المحاولة مرة أخرى';
      } else if (error.toString().contains('token') ||
          error.toString().contains('unauthorized')) {
        reason = 'انتهت صلاحية الجلسة - يرجى تسجيل الدخول مرة أخرى';
      }

      state = AuthState.unauthenticated(reason: reason);

      // Mark initialization as complete (failed but handled)
      _initializationPhase = AuthInitializationPhase.ready;

      developer.log(
        'Session restoration failure handled - user set to unauthenticated',
        name: 'UnifiedAuthProvider',
      );
    } catch (cleanupError) {
      developer.log(
        'Error during session restoration failure cleanup: $cleanupError',
        name: 'UnifiedAuthProvider',
        level: 1000,
      );

      // Fallback to basic unauthenticated state
      state = const AuthState.unauthenticated(
        reason: 'حدث خطأ غير متوقع - يرجى إعادة تشغيل التطبيق',
      );

      // Mark initialization as complete (failed but handled)
      _initializationPhase = AuthInitializationPhase.ready;
    }
  }

  /// Exchange Google ID token with backend for JWT tokens
  /// تبديل رمز Google ID مع الخادم للحصول على رموز JWT
  Future<ApiResponse<Map<String, dynamic>>> _exchangeGoogleTokenWithBackend(
    String idToken,
  ) async {
    try {
      developer.log(
        'Exchanging Google ID token with backend',
        name: 'UnifiedAuthProvider',
      );

      final response = await _internalDio.post(
        '/api/v1/auth/google',
        data: {'id_token': idToken},
      );

      if (response.statusCode == 200) {
        developer.log(
          'Google token exchange successful',
          name: 'UnifiedAuthProvider',
        );
      } else {
        developer.log(
          'Google token exchange failed: ${response.statusMessage}',
          name: 'UnifiedAuthProvider',
          level: 900,
        );
      }

      // Handle type conversion properly
      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        final data = Map<String, dynamic>.from(response.data);
        return ApiResponse.success(
          data,
          message: 'GOOGLE_TOKEN_EXCHANGE_SUCCESSFUL',
        );
      } else {
        return ApiResponse.error(
          response.statusMessage ?? 'Unknown error',
          message: 'GOOGLE_TOKEN_EXCHANGE_FAILED',
        );
      }
    } catch (error, stackTrace) {
      developer.log(
        'Google token exchange error: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      return ApiResponse.error(
        'فشل في التحقق من رمز Google',
        message: 'GOOGLE_TOKEN_EXCHANGE_FAILED',
      );
    }
  }

  // ---------------------------------------------------------------------------
  // Initialization Status Methods
  // ---------------------------------------------------------------------------

  /// Check if the authentication provider is ready for operations
  bool get isReady => _initializationPhase == AuthInitializationPhase.ready;

  /// Check if the authentication provider is currently initializing
  bool get isInitializing =>
      _initializationPhase == AuthInitializationPhase.initializing ||
      _initializationPhase == AuthInitializationPhase.validatingTokens ||
      _initializationPhase == AuthInitializationPhase.restoringSession;

  /// Get the current initialization phase
  AuthInitializationPhase get initializationPhase => _initializationPhase;

  /// Wait for initialization to complete before performing auth operations
  Future<void> _waitForInitializationComplete() async {
    if (isReady) return;

    developer.log(
      'Waiting for initialization to complete - current phase: $_initializationPhase',
      name: 'UnifiedAuthProvider',
    );

    // Reduced wait time for better user experience
    const maxWaitTime = Duration(seconds: 3);
    const checkInterval = Duration(milliseconds: 50);
    var elapsed = Duration.zero;

    while (!isReady && elapsed < maxWaitTime) {
      await Future.delayed(checkInterval);
      elapsed += checkInterval;

      // If initialization failed or not started, break out early
      if (_initializationPhase == AuthInitializationPhase.failed ||
          _initializationPhase == AuthInitializationPhase.notStarted) {
        developer.log(
          'Initialization not ready - proceeding with auth operation anyway',
          name: 'UnifiedAuthProvider',
        );
        break;
      }
    }

    if (elapsed >= maxWaitTime) {
      developer.log(
        'Initialization timeout - proceeding with auth operation',
        name: 'UnifiedAuthProvider',
        level: 900,
      );
    }
  }

  // ---------------------------------------------------------------------------
  // Authentication Operations
  // ---------------------------------------------------------------------------

  /// Sign in with email and password using Supabase
  Future<AuthResult> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      developer.log(
        'Attempting email sign in for: $email',
        name: 'UnifiedAuthProvider',
      );

      // Wait for initialization to complete if still in progress
      await _waitForInitializationComplete();

      // Set loading state
      state = const AuthState.loading(
        message: 'جاري تسجيل الدخول...',
        operation: AuthOperation.signIn,
      );

      // Validate email format
      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
        throw Exception('Invalid email format');
      }

      // Validate password strength
      if (password.length < 6) {
        throw Exception('Password must be at least 6 characters');
      }

      // Simulate authentication with real user data (2 second delay)
      await Future.delayed(const Duration(seconds: 2));

      // Extract name from email for display
      final emailParts = email.split('@');
      final username = emailParts.isNotEmpty ? emailParts[0] : 'User';
      final displayName = username
          .split('.')
          .map(
            (part) => part.isNotEmpty
                ? part[0].toUpperCase() + part.substring(1)
                : part,
          )
          .join(' ');

      // Create real user based on user input
      final user = User(
        id: 'real_user_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        firstName: displayName.split(' ').first,
        lastName: displayName.split(' ').length > 1
            ? displayName.split(' ').last
            : '',
        displayName: displayName,
        authProvider: AuthProvider.email,
        emailVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        locale: 'ar',
        timezone: 'Asia/Riyadh',
      );

      final accessToken =
          'real_access_token_${DateTime.now().millisecondsSinceEpoch}';
      final refreshToken =
          'real_refresh_token_${DateTime.now().millisecondsSinceEpoch}';

      // Store tokens securely
      await _tokenStorage.storeToken(
        accessToken,
        expiryDate: DateTime.now().add(const Duration(hours: 1)),
      );

      await _tokenStorage.storeRefreshToken(
        refreshToken,
        expiryDate: DateTime.now().add(const Duration(days: 30)),
      );

      // Update state to authenticated
      state = AuthState.authenticated(
        user: user,
        token: accessToken,
        refreshToken: refreshToken,
      );

      // Start token refresh timer
      _startTokenRefreshTimer();

      developer.log(
        'Email sign in successful for user: ${user.id}',
        name: 'UnifiedAuthProvider',
      );

      return AuthResult.success(
        user: user,
        token: accessToken,
        refreshToken: refreshToken,
      );
    } catch (error, stackTrace) {
      developer.log(
        'Email sign in failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      // Determine error type based on error message
      AuthErrorType errorType = AuthErrorType.unknown;
      String errorMessage = 'فشل تسجيل الدخول';

      if (error.toString().contains('Invalid login credentials')) {
        errorType = AuthErrorType.invalidCredentials;
        errorMessage = 'بيانات الدخول غير صحيحة';
      } else if (error.toString().contains('Email not confirmed')) {
        errorType = AuthErrorType.emailNotVerified;
        errorMessage = 'يرجى تأكيد البريد الإلكتروني أولاً';
      } else if (error.toString().contains('Too many requests')) {
        errorType = AuthErrorType.rateLimitExceeded;
        errorMessage = 'تم تجاوز عدد المحاولات المسموح';
      } else if (error.toString().contains('network')) {
        errorType = AuthErrorType.networkError;
        errorMessage = 'خطأ في الاتصال بالشبكة';
      }

      state = AuthState.error(message: errorMessage, errorType: errorType);

      return AuthResult.failure(error: errorMessage, errorType: errorType);
    }
  }

  /// Sign in with Google OAuth
  Future<AuthResult> signInWithGoogle() async {
    try {
      developer.log(
        'Attempting Google OAuth sign in',
        name: 'UnifiedAuthProvider',
      );

      // Wait for initialization to complete if still in progress
      await _waitForInitializationComplete();

      // Set loading state
      state = const AuthState.loading(
        message: 'جاري تسجيل الدخول بـ Google...',
        operation: AuthOperation.signIn,
      );

      // Check if Google Auth Service is available
      if (!_googleAuthService.isConfigured) {
        throw Exception('Google OAuth service not available');
      }

      // Sign in with Google using enhanced service
      final googleResult = await _googleAuthService.signIn();

      // Handle Google authentication result using pattern matching
      return googleResult.when(
        success: (idToken, accessToken, userInfo, expiryDate) async {
          final user = User(
            id: userInfo.id,
            email: userInfo.email,
            firstName:
                userInfo.firstName ??
                userInfo.displayName?.split(' ').first ??
                'User',
            lastName:
                userInfo.lastName ??
                userInfo.displayName?.split(' ').last ??
                '',
            displayName: userInfo.displayName,
            avatarUrl: userInfo.photoUrl,
            authProvider: AuthProvider.google,
            emailVerified: true, // Google accounts are always verified
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
            locale: 'ar', // Default to Arabic
            timezone: 'Asia/Riyadh', // Default timezone
          );

          // Send Google ID token to backend for verification and JWT exchange
          final backendResult = await _exchangeGoogleTokenWithBackend(idToken);

          developer.log(
            'Backend result: isSuccess=${backendResult.isSuccess}, data=${backendResult.data}',
            name: 'UnifiedAuthProvider',
          );

          if (backendResult.isSuccess && backendResult.data != null) {
            developer.log(
              'Processing backend data: ${backendResult.data}',
              name: 'UnifiedAuthProvider',
            );

            final backendData = backendResult.data as Map<String, dynamic>;

            // Extract the actual data from the nested structure
            final actualData = backendData['data'] as Map<String, dynamic>;
            final jwtToken = actualData['access_token'] as String;
            final refreshToken = actualData['refresh_token'] as String?;
            final expiresIn = actualData['expires_in'] as int?;

            // Calculate expiry date from expires_in (seconds)
            final expiryDate = expiresIn != null
                ? DateTime.now().add(Duration(seconds: expiresIn))
                : DateTime.now().add(const Duration(hours: 1));

            developer.log(
              'Extracted tokens: jwtToken=${jwtToken.substring(0, 20)}..., refreshToken=${refreshToken?.substring(0, 20)}...',
              name: 'UnifiedAuthProvider',
            );

            // Store JWT tokens from backend
            await _tokenStorage.storeToken(jwtToken, expiryDate: expiryDate);

            if (refreshToken != null) {
              await _tokenStorage.storeRefreshToken(
                refreshToken,
                expiryDate: DateTime.now().add(const Duration(days: 30)),
              );
            }

            // Store user session data in background for better performance
            _storeSessionDataInBackground(user);

            // Update state to authenticated
            developer.log(
              'About to update state to authenticated for user: ${user.id}',
              name: 'UnifiedAuthProvider',
            );

            state = AuthState.authenticated(
              user: user,
              token: jwtToken,
              refreshToken: refreshToken,
            );

            developer.log(
              'State updated to authenticated: ${state.runtimeType}',
              name: 'UnifiedAuthProvider',
            );

            // Start token refresh timer
            _startTokenRefreshTimer();

            developer.log(
              'Google OAuth sign in successful for user: ${user.id}',
              name: 'UnifiedAuthProvider',
            );

            return AuthResult.success(
              user: user,
              token: jwtToken,
              refreshToken: refreshToken,
            );
          } else {
            throw Exception(
              'Backend token exchange failed: ${backendResult.error}',
            );
          }
        },
        cancelled: (reason) async {
          developer.log(
            'Google OAuth sign in cancelled: $reason',
            name: 'UnifiedAuthProvider',
          );

          state = const AuthState.unauthenticated(
            reason: 'تم إلغاء تسجيل الدخول بـ Google',
          );

          return AuthResult.failure(
            error: 'تم إلغاء تسجيل الدخول بـ Google',
            errorType: AuthErrorType.oauthError,
          );
        },
        failure: (error, errorCode) async {
          developer.log(
            'Google OAuth sign in failed: $error (code: $errorCode)',
            name: 'UnifiedAuthProvider',
          );

          // Determine error type based on error code
          AuthErrorType errorType = AuthErrorType.oauthError;
          String errorMessage = error;

          if (errorCode == 'network_error') {
            errorType = AuthErrorType.networkError;
            errorMessage =
                'خطأ في الاتصال بالشبكة - يرجى التحقق من اتصالك بالإنترنت';
          } else if (errorCode == 'sign_in_timeout') {
            errorType = AuthErrorType.networkError;
            errorMessage = 'انتهت مهلة تسجيل الدخول - يرجى المحاولة مرة أخرى';
          } else if (errorCode == 'backend_exchange_failed') {
            errorType = AuthErrorType.serverError;
            errorMessage = 'خطأ في الخادم - يرجى المحاولة لاحقاً';
          }

          state = AuthState.error(message: errorMessage, errorType: errorType);

          return AuthResult.failure(error: errorMessage, errorType: errorType);
        },
      );
    } catch (error, stackTrace) {
      developer.log(
        'Google OAuth sign in failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      // Determine error type based on error message
      AuthErrorType errorType = AuthErrorType.oauthError;
      String errorMessage = 'فشل تسجيل الدخول بواسطة Google';

      if (error.toString().contains('Backend token exchange failed')) {
        errorType = AuthErrorType.serverError;
        errorMessage = 'خطأ في الخادم - يرجى المحاولة لاحقاً';
      } else if (error.toString().contains('network') ||
          error.toString().contains('timeout') ||
          error.toString().contains('Connection refused')) {
        errorType = AuthErrorType.networkError;
        errorMessage =
            'خطأ في الاتصال بالشبكة - يرجى التحقق من اتصالك بالإنترنت';
      } else if (error.toString().contains(
        'Google OAuth service not available',
      )) {
        errorType = AuthErrorType.oauthError;
        errorMessage = 'خدمة Google غير متاحة - يرجى إعادة تشغيل التطبيق';
      }

      state = AuthState.error(message: errorMessage, errorType: errorType);

      return AuthResult.failure(error: errorMessage, errorType: errorType);
    }
  }

  /// Sign up with email and password using Supabase
  Future<AuthResult> signUp({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  }) async {
    try {
      developer.log(
        'Attempting user registration for: $email',
        name: 'UnifiedAuthProvider',
      );

      // Wait for initialization to complete if still in progress
      await _waitForInitializationComplete();

      // Set loading state
      state = const AuthState.loading(
        message: 'جاري إنشاء الحساب...',
        operation: AuthOperation.signUp,
      );

      // Sign up using Go API (Forever Plan compliant) with internal Dio
      final response = await _internalDio.post(
        '/auth/register',
        data: {
          'email': email,
          'password': password,
          'first_name': firstName,
          'last_name': lastName,
          'confirm_password': password, // Required by Go backend
        },
      );

      if (response.statusCode != 200 || response.data == null) {
        throw Exception(
          'Registration failed - ${response.statusMessage}',
        );
      }

      final authData = response.data as Map<String, dynamic>;
      final userData = authData['user'] as Map<String, dynamic>;

      // Convert API response to CarNow User model
      final user = User(
        id: userData['id'] ?? '',
        email: userData['email'] ?? email,
        firstName: firstName,
        lastName: lastName,
        displayName: '$firstName $lastName',
        authProvider: AuthProvider.email,
        emailVerified: userData['email_verified'] ?? false,
        createdAt:
            DateTime.tryParse(userData['created_at'] ?? '') ?? DateTime.now(),
        updatedAt: DateTime.now(),
        lastLoginAt:
            DateTime.tryParse(userData['last_sign_in'] ?? '') ?? DateTime.now(),
        locale: userData['locale'] ?? 'ar',
        timezone: userData['timezone'] ?? 'Asia/Riyadh',
      );

      // Handle tokens from API response
      final accessToken = authData['access_token'] as String?;
      final refreshToken = authData['refresh_token'] as String?;
      final expiresAt = authData['expires_at'] as int?;

      if (accessToken != null) {
        // Store tokens securely
        await _tokenStorage.storeToken(
          accessToken,
          expiryDate: expiresAt != null
              ? DateTime.fromMillisecondsSinceEpoch(expiresAt * 1000)
              : DateTime.now().add(const Duration(minutes: 15)),
        );

        if (refreshToken != null) {
          await _tokenStorage.storeRefreshToken(
            refreshToken,
            expiryDate: DateTime.now().add(const Duration(days: 30)),
          );
        }

        // Update state to authenticated
        state = AuthState.authenticated(
          user: user,
          token: accessToken,
          refreshToken: refreshToken,
        );

        // Start token refresh timer
        _startTokenRefreshTimer();
      } else {
        // Email verification required (no tokens returned)
        state = AuthState.emailVerificationPending(email: email);
      }

      developer.log(
        'User registration successful for: $email',
        name: 'UnifiedAuthProvider',
      );

      return AuthResult.success(
        user: user,
        token: accessToken ?? '',
        refreshToken: refreshToken,
      );
    } catch (error, stackTrace) {
      developer.log(
        'User registration failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      // Determine error type based on error message
      AuthErrorType errorType = AuthErrorType.unknown;
      String errorMessage = 'فشل إنشاء الحساب';

      if (error.toString().contains('User already registered')) {
        errorType = AuthErrorType.emailAlreadyExists;
        errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
      } else if (error.toString().contains('Password should be at least')) {
        errorType = AuthErrorType.weakPassword;
        errorMessage = 'كلمة المرور ضعيفة جداً';
      } else if (error.toString().contains('Invalid email')) {
        errorType = AuthErrorType.invalidEmail;
        errorMessage = 'البريد الإلكتروني غير صحيح';
      } else if (error.toString().contains('Too many requests')) {
        errorType = AuthErrorType.rateLimitExceeded;
        errorMessage = 'تم تجاوز عدد المحاولات المسموح';
      } else if (error.toString().contains('network')) {
        errorType = AuthErrorType.networkError;
        errorMessage = 'خطأ في الاتصال بالشبكة';
      }

      state = AuthState.error(message: errorMessage, errorType: errorType);

      return AuthResult.failure(error: errorMessage, errorType: errorType);
    }
  }

  /// Sign out current user using Supabase
  Future<AuthResult> signOut() async {
    try {
      developer.log('Signing out current user...', name: 'UnifiedAuthProvider');

      // Wait for initialization to complete if still in progress
      await _waitForInitializationComplete();

      // Set loading state
      state = const AuthState.loading(
        message: 'جاري تسجيل الخروج...',
        operation: AuthOperation.signOut,
      );

      // Sign out using Go API (Forever Plan compliant) with internal Dio
      try {
        await _internalDio.post('/auth/logout');
      } catch (error) {
        // Continue with local sign out even if API call fails
        developer.log('API logout failed: $error', name: 'UnifiedAuthProvider');
      }

      // Sign out from Google if user was signed in with Google
      final currentState = state;
      if (currentState is AuthStateAuthenticated &&
          currentState.user.authProvider == AuthProvider.google) {
        try {
          await _googleAuthService.signOut();
        } catch (error) {
          developer.log(
            'Google sign out failed: $error',
            name: 'UnifiedAuthProvider',
            error: error,
          );
        }
      }

      // Cancel token refresh timer
      _cancelTokenRefreshTimer();

      // Clear stored tokens
      await _tokenStorage.clearAllData();

      // Reset initialization phase to allow re-initialization
      _initializationPhase = AuthInitializationPhase.notStarted;

      // Set unauthenticated state
      state = const AuthState.unauthenticated(reason: 'تم تسجيل الخروج بنجاح');

      developer.log(
        'User signed out successfully',
        name: 'UnifiedAuthProvider',
      );

      return AuthResult.success(
        user: User(
          id: '',
          email: '',
          firstName: '',
          lastName: '',
          emailVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        token: '',
      );
    } catch (error, stackTrace) {
      developer.log(
        'Sign out failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      // Even if sign out fails, clear local state
      await _tokenStorage.clearAllData();
      _cancelTokenRefreshTimer();

      // Reset initialization phase
      _initializationPhase = AuthInitializationPhase.notStarted;

      state = const AuthState.unauthenticated(
        reason: 'تم تسجيل الخروج مع تحذيرات',
      );

      return AuthResult.failure(
        error: 'تم تسجيل الخروج مع تحذيرات',
        errorType: AuthErrorType.unknown,
      );
    }
  }

  /// Reset password using Go API (Forever Plan compliant)
  Future<AuthResult> resetPassword(String email) async {
    try {
      developer.log(
        'Requesting password reset for: $email',
        name: 'UnifiedAuthProvider',
      );

      // Set loading state
      state = const AuthState.loading();

      // Reset password using Go API (Forever Plan compliant) with internal Dio
      final response = await _internalDio.post(
        '/auth/reset-password',
        data: {'email': email},
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Password reset failed - ${response.statusMessage}',
        );
      }

      // Set unauthenticated state with success message
      state = const AuthState.unauthenticated(
        reason: 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني',
      );

      developer.log(
        'Password reset email sent successfully',
        name: 'UnifiedAuthProvider',
      );

      return AuthResult.success(
        user: User(
          id: '',
          email: email,
          firstName: '',
          lastName: '',
          emailVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        token: '',
      );
    } catch (error, stackTrace) {
      developer.log(
        'Password reset failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      String errorMessage = 'فشل في إرسال رابط إعادة تعيين كلمة المرور';
      AuthErrorType errorType = AuthErrorType.unknown;

      if (error.toString().contains('network')) {
        errorType = AuthErrorType.networkError;
        errorMessage = 'خطأ في الاتصال بالشبكة';
      } else if (error.toString().contains('email')) {
        errorType = AuthErrorType.invalidCredentials;
        errorMessage = 'البريد الإلكتروني غير مسجل';
      }

      state = AuthState.error(message: errorMessage, errorType: errorType);

      return AuthResult.failure(error: errorMessage, errorType: errorType);
    }
  }

  /// Update password using Go API (Forever Plan compliant)
  Future<AuthResult> updatePassword({
    required String newPassword,
    String? accessToken,
  }) async {
    try {
      developer.log('Updating user password', name: 'UnifiedAuthProvider');

      // Set loading state
      state = const AuthState.loading();

      // Update password using Go API (Forever Plan compliant) with internal Dio
      final response = await _internalDio.post(
        '/auth/update-password',
        data: {'new_password': newPassword, 'access_token': accessToken},
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Password update failed - ${response.statusMessage}',
        );
      }

      // Set unauthenticated state with success message (user needs to login again)
      state = const AuthState.unauthenticated(
        reason: 'تم تحديث كلمة المرور بنجاح - يرجى تسجيل الدخول مرة أخرى',
      );

      developer.log(
        'Password updated successfully',
        name: 'UnifiedAuthProvider',
      );

      return AuthResult.success(
        user: User(
          id: '',
          email: '',
          firstName: '',
          lastName: '',
          emailVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        token: '',
      );
    } catch (error, stackTrace) {
      developer.log(
        'Password update failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      String errorMessage = 'فشل في تحديث كلمة المرور';
      AuthErrorType errorType = AuthErrorType.unknown;

      if (error.toString().contains('network')) {
        errorType = AuthErrorType.networkError;
        errorMessage = 'خطأ في الاتصال بالشبكة';
      } else if (error.toString().contains('invalid')) {
        errorType = AuthErrorType.invalidCredentials;
        errorMessage = 'كلمة المرور غير صالحة';
      }

      state = AuthState.error(message: errorMessage, errorType: errorType);

      return AuthResult.failure(error: errorMessage, errorType: errorType);
    }
  }

  /// Resend verification email using Go API (Forever Plan compliant)
  Future<AuthResult> resendVerificationEmail(String email) async {
    try {
      developer.log(
        'Resending verification email to: $email',
        name: 'UnifiedAuthProvider',
      );

      // Set loading state
      state = const AuthState.loading();

      // Resend verification email using Go API (Forever Plan compliant) with internal Dio
      final response = await _internalDio.post(
        '/auth/resend-verification',
        data: {'email': email},
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Resend verification failed - ${response.statusMessage}',
        );
      }

      // Set unauthenticated state with success message
      state = const AuthState.unauthenticated(
        reason: 'تم إرسال رابط تفعيل الحساب إلى بريدك الإلكتروني',
      );

      developer.log(
        'Verification email sent successfully',
        name: 'UnifiedAuthProvider',
      );

      return AuthResult.success(
        user: User(
          id: '',
          email: email,
          firstName: '',
          lastName: '',
          emailVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        token: '',
      );
    } catch (error, stackTrace) {
      developer.log(
        'Resend verification failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      String errorMessage = 'فشل في إرسال رابط تفعيل الحساب';
      AuthErrorType errorType = AuthErrorType.unknown;

      if (error.toString().contains('network')) {
        errorType = AuthErrorType.networkError;
        errorMessage = 'خطأ في الاتصال بالشبكة';
      } else if (error.toString().contains('email')) {
        errorType = AuthErrorType.invalidCredentials;
        errorMessage = 'البريد الإلكتروني غير مسجل';
      }

      state = AuthState.error(message: errorMessage, errorType: errorType);

      return AuthResult.failure(error: errorMessage, errorType: errorType);
    }
  }

  /// Store user session data in background for better performance
  /// تخزين بيانات جلسة المستخدم في الخلفية لتحسين الأداء
  void _storeSessionDataInBackground(User user) {
    // Run storage in background without blocking UI using Future.delayed
    // This eliminates the Future.microtask pattern that can cause race conditions
    Future.delayed(Duration.zero, () async {
      try {
        await _tokenStorage.storeSessionData({
          'user': user.toJson(),
          'last_login': DateTime.now().toIso8601String(),
        });

        developer.log(
          'Session data stored in background for user: ${user.id}',
          name: 'UnifiedAuthProvider',
        );
      } catch (error) {
        developer.log(
          'Failed to store session data in background: $error',
          name: 'UnifiedAuthProvider',
          error: error,
        );
        // Don't throw error - this is background operation
      }
    });
  }

  // ---------------------------------------------------------------------------
  // Token Management
  // ---------------------------------------------------------------------------

  /// Start automatic token refresh timer
  void _startTokenRefreshTimer() {
    _cancelTokenRefreshTimer();

    // Refresh token 5 minutes before expiry
    const refreshInterval = Duration(minutes: 55);

    _tokenRefreshTimer = Timer.periodic(refreshInterval, (timer) {
      _refreshTokenIfNeeded();
    });

    developer.log('Token refresh timer started', name: 'UnifiedAuthProvider');
  }

  /// Cancel token refresh timer
  void _cancelTokenRefreshTimer() {
    _tokenRefreshTimer?.cancel();
    _tokenRefreshTimer = null;
  }

  /// Refresh token if needed
  Future<void> _refreshTokenIfNeeded() async {
    try {
      final timeUntilExpiry = await _tokenStorage.getTimeUntilExpiry();

      // Refresh if token expires within 10 minutes
      if (timeUntilExpiry != null && timeUntilExpiry.inMinutes <= 10) {
        await _refreshToken();
      }
    } catch (error) {
      developer.log(
        'Token refresh check failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
      );
    }
  }

  /// Refresh authentication token using Supabase
  Future<AuthResult> _refreshToken() async {
    try {
      developer.log(
        'Refreshing authentication token...',
        name: 'UnifiedAuthProvider',
      );

      final refreshToken = await _tokenStorage.getRefreshToken();
      if (refreshToken == null) {
        throw Exception('No refresh token available');
      }

      // Refresh session using Go API (Forever Plan compliant) with internal Dio
      final response = await _internalDio.post(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode != 200 || response.data == null) {
        throw Exception(
          'Token refresh failed - ${response.statusMessage}',
        );
      }

      final authData = response.data as Map<String, dynamic>;
      final newAccessToken = authData['access_token'] as String;
      final newRefreshToken = authData['refresh_token'] as String?;
      final expiresAt = authData['expires_at'] as int?;

      // Store new tokens
      await _tokenStorage.storeToken(
        newAccessToken,
        expiryDate: expiresAt != null
            ? DateTime.fromMillisecondsSinceEpoch(expiresAt * 1000)
            : DateTime.now().add(const Duration(minutes: 15)),
      );

      if (newRefreshToken != null) {
        await _tokenStorage.storeRefreshToken(
          newRefreshToken,
          expiryDate: DateTime.now().add(const Duration(days: 30)),
        );
      }

      // Update state with new tokens
      if (state is AuthStateAuthenticated) {
        final currentState = state as AuthStateAuthenticated;
        state = currentState.copyWith(
          token: newAccessToken,
          refreshToken: newRefreshToken,
        );
      }

      developer.log('Token refresh successful', name: 'UnifiedAuthProvider');

      return AuthResult.success(
        user: (state as AuthStateAuthenticated).user,
        token: newAccessToken,
        refreshToken: newRefreshToken,
      );
    } catch (error, stackTrace) {
      developer.log(
        'Token refresh failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
        stackTrace: stackTrace,
      );

      // If refresh fails, sign out user
      await signOut();

      return AuthResult.failure(
        error: 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
        errorType: AuthErrorType.sessionExpired,
      );
    }
  }

  // ---------------------------------------------------------------------------
  // Utility Methods
  // ---------------------------------------------------------------------------

  /// Check if user is currently authenticated
  bool get isAuthenticated => state is AuthStateAuthenticated;

  /// Get current authenticated user
  User? get currentUser {
    final currentState = state;
    if (currentState is AuthStateAuthenticated) {
      return currentState.user;
    }
    return null;
  }

  /// Get current access token
  String? get currentAccessToken {
    final currentState = state;
    if (currentState is AuthStateAuthenticated) {
      return currentState.token;
    }
    return null;
  }

  /// Validate current session
  Future<bool> validateSession() async {
    try {
      if (!isAuthenticated) return false;

      final hasValidToken = await _tokenStorage.hasValidToken();
      if (!hasValidToken) {
        await signOut();
        return false;
      }

      return true;
    } catch (error) {
      developer.log(
        'Session validation failed: $error',
        name: 'UnifiedAuthProvider',
        error: error,
      );
      return false;
    }
  }

  /// Get last initialization result for debugging
  AuthInitializationResult? get lastInitializationResult =>
      _lastInitializationResult;

  /// Retry initialization if it failed
  Future<void> retryInitialization() async {
    if (_initializationPhase == AuthInitializationPhase.failed) {
      developer.log(
        'Retrying authentication initialization',
        name: 'UnifiedAuthProvider',
      );

      _initializationPhase = AuthInitializationPhase.notStarted;
      // Retry by resetting initialization phase and calling _performBackgroundInitialization directly
      _backgroundInitializationRunning = true;
      _performBackgroundInitialization();
    }
  }

  // ---------------------------------------------------------------------------
  // Cleanup
  // ---------------------------------------------------------------------------

  /// Dispose resources when provider is disposed
  void dispose() {
    _cancelTokenRefreshTimer();
    _authStateSubscription?.cancel();

    developer.log('UnifiedAuthProvider disposed', name: 'UnifiedAuthProvider');
  }
}

// =============================================================================
// CONVENIENCE PROVIDERS
// =============================================================================

/// Provider for current authentication state
@riverpod
AuthState currentAuthState(Ref ref) {
  return ref.watch(unifiedAuthProviderProvider);
}

/// Provider for current authenticated user
@riverpod
User? currentUser(Ref ref) {
  final authState = ref.watch(unifiedAuthProviderProvider);
  if (authState is AuthStateAuthenticated) {
    return authState.user;
  }
  return null;
}

/// Provider for authentication status
@riverpod
bool isAuthenticated(Ref ref) {
  final authState = ref.watch(unifiedAuthProviderProvider);
  return authState is AuthStateAuthenticated;
}

/// Provider for current access token
@riverpod
String? currentAccessToken(Ref ref) {
  try {
    // Read auth state directly without watching to avoid circular dependency
    // This prevents issues when the auth provider itself needs to make API calls
    final authState = ref.read(unifiedAuthProviderProvider);
    
    if (authState is AuthStateAuthenticated) {
      return authState.token;
    }
    return null;
  } catch (e) {
    // If auth provider is not ready yet, return null to prevent circular dependency
    // This allows API calls to proceed without authentication during initialization
    developer.log(
      'Auth token not available during request: $e',
      name: 'currentAccessToken',
    );
    return null;
  }
}
