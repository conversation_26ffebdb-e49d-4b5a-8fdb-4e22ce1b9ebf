package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/handlers"
	"carnow-backend/internal/infrastructure/cache"
	"carnow-backend/internal/infrastructure/database"
	"carnow-backend/internal/services"
	"carnow-backend/internal/shared/middleware"
	"carnow-backend/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

func main() {
	log.Println("🚀 Starting CarNow Backend (Forever Plan - Single Database Architecture)...")

	// Load configuration (Forever Plan - Simple)
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	log.Printf("✅ Configuration loaded (Environment: %s, Port: %d)", cfg.App.Environment, cfg.Server.Port)

	// Initialize logger
	appLogger, err := logger.NewLogger(cfg.Logging.Level, cfg.Logging.Format)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	// Initialize cache service
	log.Println("🔄 Initializing Redis cache infrastructure...")
	if err := cache.InitializeCacheProvider(cfg, appLogger.Logger); err != nil {
		log.Printf("⚠️ Cache initialization failed: %v", err)
		log.Println("🔄 Continuing without cache...")
	} else {
		log.Println("✅ Redis cache infrastructure initialized successfully")

		// Warm cache on startup
		cacheProvider := cache.GetCacheProvider()
		if cacheProvider.IsEnabled() {
			go func() {
				log.Println("🔥 Starting cache warming...")
				if err := cacheProvider.WarmCache(context.Background()); err != nil {
					log.Printf("⚠️ Cache warming failed: %v", err)
				} else {
					log.Println("✅ Cache warming completed")
				}
			}()
		}
	}

	// Production-ready database initialization with fallback
	var db *database.SimpleDB

	log.Println("🔄 Initializing single database connection...")
	db, err = database.NewSimpleDB(cfg)
	if err != nil {
		log.Printf("⚠️ Database connection failed: %v", err)
		log.Println("🔄 Starting in fallback mode without database...")

		// Create mock database for fallback mode
		db = nil
	} else {
		log.Println("✅ Database connection established successfully")
	}

	// Initialize SimpleAPI with GORM database
	var simpleAPI *handlers.SimpleAPI
	var dbConnection *database.Database
	dbConnection, err = database.NewDatabase(cfg)
	if err != nil {
		log.Printf("⚠️ GORM Database connection failed: %v", err)
		log.Println("🔄 Creating SimpleAPI with nil database for fallback mode")
		simpleAPI = handlers.NewSimpleAPI(nil)
	} else {
		log.Println("✅ GORM Database connection established successfully")
		simpleAPI = handlers.NewSimpleAPI(dbConnection.DB)
	}

	// Initialize clean API handlers (Forever Plan - simple architecture only)
	// NewCleanAPI now handles nil database for fallback mode
	api := handlers.NewCleanAPI(db)

	// Initialize seller subscription service if GORM database is available
	if dbConnection != nil {
		api.SellerSubscriptionService = services.NewSellerSubscriptionService(dbConnection.DB, appLogger.Logger)
	}

	// Initialize seller subscription handler
	var sellerSubscriptionHandler *handlers.SellerSubscriptionHandler
	if dbConnection != nil {
		sellerSubscriptionHandler = handlers.NewSellerSubscriptionHandler(api.SellerSubscriptionService, appLogger.Logger)
	}

	// Initialize WebSocket service and handler for real-time updates
	wsService := services.NewWebSocketService(appLogger.Logger)
	wsHandler := handlers.NewWebSocketHandler(wsService, appLogger.Logger)

	// Initialize image processing service and handler
	imageConfig := &services.ImageConfig{
		MaxFileSize:    10 * 1024 * 1024, // 10MB
		Quality:        85,
		WebPQuality:    80,
		EnableWebP:     true,
		EnableAVIF:     false,
		ThumbnailSizes: []int{150, 300, 600},
		UploadPath:     "./uploads/images",
	}
	imageService := services.NewImageService(db.Pool, imageConfig, appLogger.Logger)
	imageHandler := handlers.NewImageHandler(imageService, appLogger.Logger)

	// Initialize enhanced cart service and handler (Forever Plan architecture)
	var cartHandler *handlers.CartHandler
	var orderHandler *handlers.OrderHandler
	if db != nil {
		// Get Redis client from cache provider (can be nil if Redis is disabled)
		var redisClient *redis.Client
		if cacheProvider := cache.GetCacheProvider(); cacheProvider != nil && cacheProvider.IsEnabled() {
			// Access Redis client through the cache service
			if cacheService := cacheProvider.GetService(); cacheService != nil {
				// Redis client is internal to cache service, pass nil for now
				// The cart service will handle nil Redis client gracefully
				redisClient = nil
			}
		}

		// Initialize cart service with pgx pool
		cartService := services.NewCartService(db.Pool, redisClient, wsService, appLogger.Logger)

		// Initialize cart handler
		cartHandler = handlers.NewCartHandler(cartService, appLogger.Logger)

		// Initialize notification service
		notificationConfig := &services.NotificationConfig{
			SMTPHost:     "smtp.gmail.com",
			SMTPPort:     587,
			SMTPUsername: "<EMAIL>",
			SMTPPassword: "password", // Should be from environment
			FromEmail:    "<EMAIL>",
			FromName:     "CarNow",
		}
		notificationService := services.NewNotificationService(db.Pool, notificationConfig, appLogger.Logger)

		// Initialize order service and handler (integrates with existing wallet system)
		orderService := services.NewOrderService(db.Pool, cartService, notificationService, wsService, appLogger.Logger)
		orderHandler = handlers.NewOrderHandler(orderService, appLogger.Logger)

		// Pass WebSocket service to other services for real-time notifications
		if cartService != nil {
			// TODO: Add WebSocket integration to cart service
		}
		if orderService != nil {
			// TODO: Add WebSocket integration to order service
		}
	}

	// Test database health
	if db != nil {
		if err := db.Health(); err != nil {
			log.Printf("⚠️ Database health check failed: %v", err)
			log.Println("🔄 Continuing with database connection (health check may be temporary)")
		} else {
			log.Println("✅ Database health check passed")
		}
		log.Println("✅ Forever Plan: Using single database connection only")
	} else {
		log.Println("✅ Forever Plan: Running in fallback mode (no database connection)")
	}

	// Set Gin mode
	if cfg.App.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.Default()

	// Global middlewares
	router.Use(middleware.RecoveryWithLogger())

	// Initialize and use Circuit Breaker middleware
	cb := middleware.NewCircuitBreaker("main_api", middleware.DefaultCircuitBreakerConfig(), appLogger.Logger)
	// Note: Circuit breaker middleware temporarily disabled until gin.HandlerFunc compatibility is added
	// router.Use(middleware.CircuitBreakerMiddleware(cb))
	_ = cb // Prevent unused variable error

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})

	// Add cache middleware for appropriate endpoints
	cacheProvider := cache.GetCacheProvider()
	if cacheProvider.IsEnabled() {
		log.Println("✅ Cache middleware enabled for performance optimization")
	}

	// Root endpoint - Welcome message
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message":      "مرحباً بك في CarNow Backend API",
			"message_en":   "Welcome to CarNow Backend API",
			"version":      "v1.0.0",
			"status":       "running",
			"architecture": "Forever Plan: Flutter UI Only → Go API → Supabase Data",
			"endpoints": gin.H{
				"health":     "/health",
				"api_v1":     "/api/v1",
				"auth":       "/api/v1/auth",
				"products":   "/api/v1/products",
				"categories": "/api/v1/categories",
			},
			"timestamp": time.Now(),
		})
	})

	// Health check endpoints
	router.GET("/health", api.Health)

	// Cache metrics endpoint (for monitoring)
	if cacheProvider.IsEnabled() {
		router.GET("/cache/metrics", func(c *gin.Context) {
			metrics := cacheProvider.GetMetrics()
			c.JSON(http.StatusOK, gin.H{
				"cache_metrics": metrics,
				"timestamp":     time.Now(),
			})
		})

		router.GET("/cache/health", func(c *gin.Context) {
			if err := cacheProvider.Health(c.Request.Context()); err != nil {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"status": "unhealthy",
					"error":  err.Error(),
				})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"status": "healthy",
			})
		})
	}

	// Public API routes (no authentication required)
	public := router.Group("/api/v1")
	{
		public.GET("/health", api.Health)

		// Cached endpoints for better performance
		if cacheProvider.IsEnabled() {
			public.GET("/products", middleware.CacheProductsMiddleware(cacheProvider, appLogger.Logger), api.GetProducts)           // Cached products endpoint
			public.GET("/categories", middleware.CacheCategoriesMiddleware(cacheProvider, appLogger.Logger), api.GetCategories)     // Cached categories endpoint
			public.GET("/search/products", middleware.CacheProductsMiddleware(cacheProvider, appLogger.Logger), api.SearchProducts) // Cached search endpoint
		} else {
			public.GET("/products", api.GetProducts)           // Clean products endpoint
			public.GET("/categories", api.GetCategories)       // Clean categories endpoint
			public.GET("/search/products", api.SearchProducts) // Product search endpoint
		}

		// Authentication endpoints (public - no auth required)
		public.POST("/auth/login", api.SignIn)                 // User login
		public.POST("/auth/register", api.SignUp)              // User registration
		public.POST("/auth/google", api.GoogleAuth)            // Google OAuth login
		public.POST("/auth/refresh", api.RefreshToken)         // Token refresh
		public.POST("/auth/logout", api.SignOut)               // User logout (public for resilience)
		public.POST("/auth/reset-password", api.ResetPassword) // Password reset

		// Cities endpoint (public - no auth required)
		public.GET("/cities", simpleAPI.GetCities) // Get cities for seller registration
		public.GET("/subscription-plans", simpleAPI.GetSubscriptionPlans)

		// Test endpoint for cart functionality (no auth required)
		public.GET("/test/cart", api.TestCartEndpoint)           // Test cart endpoints
		public.POST("/test/cart/mock", api.TestCartWithMockUser) // Test cart with mock user
	}

	// Protected API routes (authentication required)
	protected := router.Group("/api/v1")
	protected.Use(middleware.SimpleJWTMiddleware(cfg, db))
	{
		// Authentication endpoints (protected - auth required)
		protected.POST("/auth/change-password", api.ChangePassword) // Password change

		// TASK 5: Essential Go Backend API Endpoints
		protected.GET("/user/profile", api.GetUserProfile)    // Clean user profile endpoint
		protected.PUT("/user/profile", api.UpdateUserProfile) // Update user profile endpoint

		// Wallet Management APIs (Task 2.1.2)
		protected.GET("/wallets/user", api.GetWallet)                        // Get user wallet
		protected.GET("/wallets/transactions", api.GetWalletTransactions)    // Get wallet transactions
		protected.POST("/wallets/transactions", api.CreateWalletTransaction) // Create transaction (deposit/withdraw)

		// TASK 6: File Storage Endpoints in Go Backend
		protected.POST("/storage/upload", api.UploadFile)   // File upload endpoint
		protected.DELETE("/storage/delete", api.DeleteFile) // File deletion endpoint
		protected.GET("/storage/url", api.GetStorageURL)    // Public URL generation endpoint

		// TASK 7: Seller Management API Endpoints
		protected.POST("/seller/request", api.CreateSeller)           // Request seller status
		protected.GET("/seller", api.GetSeller)                       // Get current user's seller profile
		protected.PUT("/seller", api.UpdateSeller)                    // Update seller profile
		protected.POST("/seller/documents", api.UploadSellerDocument) // Upload seller documents
		protected.GET("/seller/documents", api.GetSellerDocuments)    // Get seller documents

		// Seller Subscription API Endpoints
		if sellerSubscriptionHandler != nil {
			protected.POST("/seller/subscription/request", sellerSubscriptionHandler.CreateSellerSubscriptionRequest)                     // Create subscription request
			protected.GET("/seller/subscription/request", sellerSubscriptionHandler.GetSellerCurrentSubscriptionRequest)                  // Get current subscription request
			protected.DELETE("/seller/subscription/request", sellerSubscriptionHandler.CancelSellerSubscriptionRequest)                   // Cancel subscription request
			protected.GET("/seller/subscription/requests", sellerSubscriptionHandler.GetAllSellerSubscriptionRequests)                    // Get all subscription requests (admin)
			protected.PUT("/seller/subscription/request/:id/status", sellerSubscriptionHandler.UpdateSellerSubscriptionRequestStatus)     // Update request status (admin)
			protected.PUT("/seller/subscription/request/:id/priority", sellerSubscriptionHandler.UpdateSellerSubscriptionRequestPriority) // Update request priority (admin)
		}

		// TASK 7: Admin seller management endpoints
		protected.GET("/seller/requests", api.GetAllSellers)         // Get all seller requests (admin)
		protected.PUT("/seller/approve/:id", api.UpdateSellerStatus) // Approve/reject seller (admin)
		protected.PUT("/seller/reject/:id", api.UpdateSellerStatus)  // Approve/reject seller (admin)

		// TASK 2.2.3: Product Management APIs - Complete product management
		protected.POST("/products", api.CreateProduct)       // Create new product
		protected.PUT("/products/:id", api.UpdateProduct)    // Update product
		protected.DELETE("/products/:id", api.DeleteProduct) // Delete product

		// TASK 2.2.3: Order Management APIs - Complete order processing workflow
		// Note: Order routes are handled below with conditional logic

		// TASK 2.2.4: Cart Management APIs - Enhanced Forever Plan architecture
		if cartHandler != nil {
			// Use enhanced cart handler with proper service layer
			protected.GET("/cart", cartHandler.GetCart)                     // Get user's cart with totals
			protected.GET("/cart/items", cartHandler.GetCartItems)          // Get user's cart items (legacy)
			protected.POST("/cart/items", cartHandler.AddCartItem)          // Add item to cart
			protected.PUT("/cart/items/:id", cartHandler.UpdateCartItem)    // Update cart item quantity
			protected.DELETE("/cart/items/:id", cartHandler.RemoveCartItem) // Remove item from cart
			protected.DELETE("/cart", cartHandler.ClearCart)                // Clear all cart items
		} else {
			// Fallback to old API handlers if cart handler is not available
			protected.GET("/cart/items", api.GetCartItems)          // Get user's cart items
			protected.POST("/cart/items", api.AddCartItem)          // Add item to cart
			protected.PUT("/cart/items/:id", api.UpdateCartItem)    // Update cart item quantity
			protected.DELETE("/cart/items/:id", api.RemoveCartItem) // Remove item from cart
			protected.DELETE("/cart/clear", api.ClearCart)          // Clear all cart items
		}

		// TASK 2.2.5: Order Management APIs - Integrates with existing wallet system
		if orderHandler != nil {
			// Enhanced order management with wallet integration
			protected.POST("/orders", orderHandler.CreateOrder)        // Create order from cart
			protected.GET("/orders", orderHandler.GetUserOrders)       // Get user's orders
			protected.GET("/orders/:id", orderHandler.GetOrderDetails) // Get order details
		} else {
			// Fallback to existing order APIs if available
			protected.GET("/orders", api.GetUserOrders)
			protected.POST("/orders", api.CreateOrder)
		}

		// TASK 2.2.6: WebSocket Real-time Updates
		protected.GET("/ws", wsHandler.HandleWebSocketConnection) // WebSocket connection
		protected.GET("/ws/stats", wsHandler.GetWebSocketStats)   // WebSocket statistics

		// TASK 2.2.7: Image Processing APIs
		protected.POST("/images/products", imageHandler.UploadProductImage)         // Upload single product image
		protected.POST("/images/products/batch", imageHandler.UploadMultipleImages) // Upload multiple images
		protected.GET("/images/:id/info", imageHandler.GetImageInfo)                // Get image information

		// Public image serving (no auth required)
		router.GET("/api/v1/images/:id/:filename", imageHandler.GetImage) // Serve processed images

		// TASK 8: Analytics endpoints (protected)
		protected.GET("/analytics/sales", api.GetSalesAnalytics)         // Sales analytics
		protected.GET("/analytics/inventory", api.GetInventoryAnalytics) // Inventory analytics
	}

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in goroutine
	go func() {
		log.Printf("🌐 Server starting on port %d", cfg.Server.Port)
		log.Println("📋 Clean API endpoints (Forever Plan):")
		log.Println("  PUBLIC ENDPOINTS:")
		log.Println("  • GET  /health - Health check")
		log.Println("  • GET  /api/v1/products - Products with pagination & filtering")
		log.Println("  • GET  /api/v1/categories - Categories with hierarchical support")
		log.Println("  PROTECTED ENDPOINTS (JWT Required):")
		log.Println("  • GET  /api/v1/user/profile - Get user profile")
		log.Println("  • PUT  /api/v1/user/profile - Update user profile")
		log.Println("  • POST /api/v1/storage/upload - Upload files")
		log.Println("  • DELETE /api/v1/storage/delete - Delete files")
		log.Println("  • GET  /api/v1/storage/url - Generate public URLs")
		log.Println("✅ TASK 5 & 6 Complete: Essential APIs + File Storage implemented")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("🛑 Shutting down server...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Server.GracefulTimeout)
	defer cancel()

	// Close cache connections
	if cacheProvider.IsEnabled() {
		log.Println("🔄 Closing cache connections...")
		if err := cacheProvider.Close(); err != nil {
			log.Printf("⚠️ Error closing cache: %v", err)
		} else {
			log.Println("✅ Cache connections closed")
		}
	}

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("✅ Server exited successfully")
}
