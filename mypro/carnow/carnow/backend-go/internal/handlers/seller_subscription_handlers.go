package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/core/models"
	"carnow-backend/internal/services"
)

// SellerSubscriptionHandler handles seller subscription request HTTP requests
type SellerSubscriptionHandler struct {
	sellerSubscriptionService *services.SellerSubscriptionService
	logger                    *zap.Logger
}

// NewSellerSubscriptionHandler creates a new seller subscription handler
func NewSellerSubscriptionHandler(sellerSubscriptionService *services.SellerSubscriptionService, logger *zap.Logger) *SellerSubscriptionHandler {
	return &SellerSubscriptionHandler{
		sellerSubscriptionService: sellerSubscriptionService,
		logger:                    logger,
	}
}

// CreateSellerSubscriptionRequest creates a new seller subscription request
// @Summary Create seller subscription request
// @Description Create a new seller subscription request for the authenticated user
// @Tags Seller Subscriptions
// @Accept json
// @Produce json
// @Param request body CreateSellerSubscriptionRequest true "Seller subscription request creation request"
// @Success 201 {object} domain.SellerSubscriptionRequest
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Router /api/v1/seller/subscription-requests [post]
func (h *SellerSubscriptionHandler) CreateSellerSubscriptionRequest(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	var req CreateSellerSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request body: " + err.Error()),
		})
		return
	}

	// Validate required fields
	if req.PlanID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Plan ID is required"),
		})
		return
	}

	if req.RequestedTier == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Requested tier is required"),
		})
		return
	}

	if req.BillingCycle == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Billing cycle is required"),
		})
		return
	}

	if req.BillingCycle != "monthly" && req.BillingCycle != "yearly" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Billing cycle must be 'monthly' or 'yearly'"),
		})
		return
	}

	if req.RequestedPriceLD <= 0 {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Requested price must be greater than 0"),
		})
		return
	}

	request, err := h.sellerSubscriptionService.CreateSellerSubscriptionRequest(
		c.Request.Context(),
		userIDStr.(string),
		req.PlanID,
		req.RequestedTier,
		req.BillingCycle,
		req.RequestedPriceLD,
	)
	if err != nil {
		h.logger.Error("Failed to create seller subscription request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to create seller subscription request: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Data:    request,
		Message: stringPtr("Seller subscription request submitted successfully"),
	})
}

// GetSellerCurrentSubscriptionRequest returns the current subscription request for the authenticated seller
// @Summary Get seller current subscription request
// @Description Get the current subscription request for the authenticated seller
// @Tags Seller Subscriptions
// @Accept json
// @Produce json
// @Success 200 {object} domain.SellerSubscriptionRequest
// @Failure 401 {object} gin.H
// @Router /api/v1/seller/subscription-requests/current [get]
func (h *SellerSubscriptionHandler) GetSellerCurrentSubscriptionRequest(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	request, err := h.sellerSubscriptionService.GetSellerCurrentSubscriptionRequest(c.Request.Context(), userIDStr.(string))
	if err != nil {
		h.logger.Error("Failed to get seller current subscription request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to fetch subscription request: " + err.Error()),
		})
		return
	}

	if request == nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   stringPtr("No current subscription request found"),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    request,
	})
}

// CancelSellerSubscriptionRequest cancels the seller's current subscription request
// @Summary Cancel seller subscription request
// @Description Cancel the current subscription request for the authenticated seller
// @Tags Seller Subscriptions
// @Accept json
// @Produce json
// @Param id path string true "Request ID"
// @Success 200 {object} gin.H
// @Failure 401 {object} gin.H
// @Router /api/v1/seller/subscription-requests/{id}/cancel [post]
func (h *SellerSubscriptionHandler) CancelSellerSubscriptionRequest(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	requestID := c.Param("id")
	if requestID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Request ID is required"),
		})
		return
	}

	err := h.sellerSubscriptionService.CancelSellerSubscriptionRequest(c.Request.Context(), requestID, userIDStr.(string))
	if err != nil {
		h.logger.Error("Failed to cancel seller subscription request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to cancel subscription request: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: stringPtr("Subscription request cancelled successfully"),
	})
}

// GetAllSellerSubscriptionRequests returns all seller subscription requests (admin only)
// @Summary Get all seller subscription requests
// @Description Get all seller subscription requests with optional status filter
// @Tags Seller Subscriptions (Admin)
// @Accept json
// @Produce json
// @Param status query string false "Filter by status"
// @Param limit query int false "Limit results (default: 50)"
// @Param offset query int false "Offset results (default: 0)"
// @Success 200 {array} domain.SellerSubscriptionRequest
// @Failure 401 {object} gin.H
// @Router /api/v1/admin/seller/subscription-requests [get]
func (h *SellerSubscriptionHandler) GetAllSellerSubscriptionRequests(c *gin.Context) {
	// TODO: Add admin authentication check

	statusFilter := c.Query("status")
	var statusFilterPtr *string
	if statusFilter != "" {
		statusFilterPtr = &statusFilter
	}

	limit := 50
	if limitStr := c.Query("limit"); limitStr != "" {
		// Parse limit with error handling
		// In a real implementation, you would parse this properly
	}

	offset := 0
	if offsetStr := c.Query("offset"); offsetStr != "" {
		// Parse offset with error handling
		// In a real implementation, you would parse this properly
	}

	requests, err := h.sellerSubscriptionService.GetAllSellerSubscriptionRequests(c.Request.Context(), statusFilterPtr, limit, offset)
	if err != nil {
		h.logger.Error("Failed to get seller subscription requests", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to fetch subscription requests: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    requests,
	})
}

// UpdateSellerSubscriptionRequestStatus updates the status of a seller subscription request (admin only)
// @Summary Update seller subscription request status
// @Description Update the status of a seller subscription request (admin only)
// @Tags Seller Subscriptions (Admin)
// @Accept json
// @Produce json
// @Param id path string true "Request ID"
// @Param request body UpdateSellerSubscriptionRequestStatusRequest true "Status update request"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Router /api/v1/admin/seller/subscription-requests/{id}/status [put]
func (h *SellerSubscriptionHandler) UpdateSellerSubscriptionRequestStatus(c *gin.Context) {
	// TODO: Add admin authentication check

	requestID := c.Param("id")
	if requestID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Request ID is required"),
		})
		return
	}

	var req UpdateSellerSubscriptionRequestStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request body: " + err.Error()),
		})
		return
	}

	// Get admin ID from JWT context
	adminIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	err := h.sellerSubscriptionService.UpdateSellerSubscriptionRequestStatus(
		c.Request.Context(),
		requestID,
		adminIDStr.(string),
		req.Status,
		req.AdminNotes,
		req.RejectionReason,
	)
	if err != nil {
		h.logger.Error("Failed to update seller subscription request status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to update subscription request status: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: stringPtr("Subscription request status updated successfully"),
	})
}

// UpdateSellerSubscriptionRequestPriority updates the priority of a seller subscription request (admin only)
// @Summary Update seller subscription request priority
// @Description Update the priority of a seller subscription request (admin only)
// @Tags Seller Subscriptions (Admin)
// @Accept json
// @Produce json
// @Param id path string true "Request ID"
// @Param request body UpdateSellerSubscriptionRequestPriorityRequest true "Priority update request"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Router /api/v1/admin/seller/subscription-requests/{id}/priority [put]
func (h *SellerSubscriptionHandler) UpdateSellerSubscriptionRequestPriority(c *gin.Context) {
	// TODO: Add admin authentication check

	requestID := c.Param("id")
	if requestID == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Request ID is required"),
		})
		return
	}

	var req UpdateSellerSubscriptionRequestPriorityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request body: " + err.Error()),
		})
		return
	}

	// Get admin ID from JWT context
	adminIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	err := h.sellerSubscriptionService.UpdateSellerSubscriptionRequestPriority(
		c.Request.Context(),
		requestID,
		adminIDStr.(string),
		req.Priority,
	)
	if err != nil {
		h.logger.Error("Failed to update seller subscription request priority", zap.Error(err))
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to update subscription request priority: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: stringPtr("Subscription request priority updated successfully"),
	})
}

// CreateSellerSubscriptionRequest represents the request body for creating a seller subscription request
type CreateSellerSubscriptionRequest struct {
	PlanID           string  `json:"plan_id" binding:"required"`
	RequestedTier    string  `json:"requested_tier" binding:"required"` // basic, premium, enterprise
	BillingCycle     string  `json:"billing_cycle" binding:"required"`  // monthly, yearly
	RequestedPriceLD float64 `json:"requested_price_ld" binding:"required"` // Price in Libyan Dinar
}

// UpdateSellerSubscriptionRequestStatusRequest represents the request body for updating a seller subscription request status
type UpdateSellerSubscriptionRequestStatusRequest struct {
	Status         string  `json:"status" binding:"required"` // under_review, approved, rejected
	AdminNotes     *string `json:"admin_notes"`               // Notes from admin
	RejectionReason *string `json:"rejection_reason"`          // Reason for rejection if rejected
}

// UpdateSellerSubscriptionRequestPriorityRequest represents the request body for updating a seller subscription request priority
type UpdateSellerSubscriptionRequestPriorityRequest struct {
	Priority int `json:"priority" binding:"required"` // Review priority (1-5)
}

// Helper function to create int64 pointer
