package handlers

import (
	"fmt"
	"log"
	"net/http"
	"strconv"



	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	domain "carnow-backend/internal/core/domain"
	admindomain "carnow-backend/internal/modules/admin/domain"
	"time"
)

// SimpleAPI contains the database connection for simple operations
type SimpleAPI struct {
	DB *gorm.DB
}

// NewSimpleAPI creates a new simple API handler
func NewSimpleAPI(db *gorm.DB) *SimpleAPI {
	return &SimpleAPI{DB: db}
}

// Health check endpoint - always available
func (s *SimpleAPI) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"message":   "CarNow Backend is running (Forever Plan)",
		"timestamp": "now",
	})
}

// Simple test endpoint to debug database
func (s *SimpleAPI) TestDB(c *gin.Context) {
	// Test simple raw query
	var count int64

	// Try the simplest possible query
	err := s.DB.Raw("SELECT COUNT(*) FROM public.\"Products\" WHERE is_active = true").Scan(&count).Error
	if err != nil {
		log.Printf("❌ Raw count query failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Raw query failed",
			"details": err.Error(),
		})
		return
	}

	log.Printf("✅ Raw count query successful: %d products", count)

	// Try getting a few products with raw query
	type SimpleProduct struct {
		ID    string  `json:"id"`
		Name  string  `json:"name"`
		Price float64 `json:"price"`
	}

	var products []SimpleProduct
	err = s.DB.Raw("SELECT id, name_en, price FROM public.\"Products\" WHERE is_active = true LIMIT 5").Scan(&products).Error
	if err != nil {
		log.Printf("❌ Raw products query failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Raw products query failed",
			"details": err.Error(),
		})
		return
	}

	log.Printf("✅ Raw products query successful: %d products found", len(products))

	c.JSON(http.StatusOK, gin.H{
		"status":          "database_working",
		"total_products":  count,
		"sample_products": products,
	})
}

// Simple products endpoint using raw SQL to avoid GORM issues
func (s *SimpleAPI) GetProductsSimple(c *gin.Context) {
	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Use raw SQL query to avoid GORM model issues
	baseQuery := "SELECT id, name_en, description_en, price, created_at, updated_at FROM public.\"Products\" WHERE is_active = true ORDER BY created_at DESC LIMIT ? OFFSET ?"
	countQuery := "SELECT COUNT(*) FROM public.\"Products\" WHERE is_active = true"

	// Simple product struct
	type SimpleProduct struct {
		ID          string    `json:"id"`
		Name        string    `json:"name"`
		Description *string   `json:"description"`
		Price       float64   `json:"price"`
		CreatedAt   time.Time `json:"created_at"`
		UpdatedAt   time.Time `json:"updated_at"`
	}

	// Get total count
	var total int64
	if err := s.DB.Raw(countQuery).Scan(&total).Error; err != nil {
		log.Printf("❌ Count query failed: %v", err)
		total = 0 // Fallback
	}

	// Get products
	var products []SimpleProduct
	if err := s.DB.Raw(baseQuery, limit, offset).Scan(&products).Error; err != nil {
		log.Printf("❌ Products query failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch products",
			"details": err.Error(),
		})
		return
	}

	// Calculate pagination metadata
	totalPages := (int(total) + limit - 1) / limit
	hasMore := len(products) == limit && page < totalPages

	log.Printf("✅ Simple products query successful: %d products found, page %d of %d", len(products), page, totalPages)

	// Return paginated response compatible with Flutter ProductsResult
	c.JSON(http.StatusOK, gin.H{
		"products": products,
		"pagination": gin.H{
			"current_page": page,
			"total_pages":  totalPages,
			"total_count":  total,
			"has_more":     hasMore,
			"limit":        limit,
		},
	})
}

// CreateUser creates a new user
func (s *SimpleAPI) CreateUser(c *gin.Context) {
	var req struct {
		Email    string `json:"email" binding:"required,email"`
		FullName string `json:"full_name"`
		Username string `json:"username"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	user := domain.User{
		ID:       uuid.New(),
		Email:    req.Email,
		FullName: &req.FullName,
		Username: &req.Username,
		Role:     "user",
		Status:   "active",
		IsActive: true,
	}

	if err := s.DB.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	c.JSON(http.StatusCreated, user)
}

// GetUser gets a user by ID
func (s *SimpleAPI) GetUser(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var user domain.User
	if err := s.DB.Where("id = ? AND is_deleted = false", userID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// GetUsers gets all users
func (s *SimpleAPI) GetUsers(c *gin.Context) {
	var users []domain.User
	if err := s.DB.Where("is_deleted = false").Find(&users).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// GetAllUsers gets all users for admin
func (s *SimpleAPI) GetAllUsers(c *gin.Context) {
	// Check if current user is admin
	if !s.isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	var users []domain.User
	if err := s.DB.Where("is_deleted = false").Find(&users).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// AssignAdmin assigns admin role to a user
func (s *SimpleAPI) AssignAdmin(c *gin.Context) {
	// Check if current user is admin
	if !s.isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	var req struct {
		UserID uuid.UUID `json:"user_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var user domain.User
	if err := s.DB.First(&user, req.UserID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	user.Role = "admin"
	if err := s.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign admin"})
		return
	}

	// Log action
	s.logAdminAction(c, req.UserID, "assign_admin", "Assigned admin role")

	c.JSON(http.StatusOK, gin.H{"message": "Admin assigned successfully"})
}

// RemoveAdmin removes admin role from a user
func (s *SimpleAPI) RemoveAdmin(c *gin.Context) {
	// Check if current user is admin
	if !s.isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	var req struct {
		UserID uuid.UUID `json:"user_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	currentUserID := c.GetString("user_id")
	if currentUserID == req.UserID.String() {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot remove yourself as admin"})
		return
	}

	var user domain.User
	if err := s.DB.First(&user, req.UserID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	user.Role = "user"
	if err := s.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove admin"})
		return
	}

	// Log action
	s.logAdminAction(c, req.UserID, "remove_admin", "Removed admin role")

	c.JSON(http.StatusOK, gin.H{"message": "Admin removed successfully"})
}

// IsAdmin checks if a user is admin
func (s *SimpleAPI) IsAdmin(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var user domain.User
	if err := s.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"is_admin": user.Role == "admin"})
}

// LogAdminAction logs an admin action
func (s *SimpleAPI) LogAdminAction(c *gin.Context) {
	// This can be called internally or via API if needed
	var req struct {
		UserID     uuid.UUID `json:"user_id"`
		AdminID    uuid.UUID `json:"admin_id"`
		ActionType string    `json:"action_type"`
		Reason     string    `json:"reason"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Implement logging to a table if needed
	// For now, perhaps log to console or a log table
	c.JSON(http.StatusOK, gin.H{"message": "Action logged"})
}

// Helper: isAdmin checks if current user is admin
func (s *SimpleAPI) isAdmin(c *gin.Context) bool {
	userIDStr, exists := c.Get("user_id")
	if !exists {
		return false
	}
	userID, _ := uuid.Parse(userIDStr.(string))

	var user domain.User
	if err := s.DB.First(&user, userID).Error; err != nil {
		return false
	}
	return user.Role == "admin"
}

// Helper: logAdminAction logs the action
func (s *SimpleAPI) logAdminAction(c *gin.Context, userID uuid.UUID, actionType string, reason string) {
	adminIDStr := c.GetString("user_id")
	adminID, _ := uuid.Parse(adminIDStr)

	var adminUser domain.User
	if err := s.DB.First(&adminUser, adminID).Error; err != nil {
		return
	}

	targetUserID := userID.String()

	// Create log entry
	logEntry := admindomain.AdminActionLog{
		ID:           uuid.New().String(),
		AdminUserID:  adminID.String(),
		AdminEmail:   adminUser.Email,
		ActionType:   admindomain.AdminActionType(actionType),
		TargetUserID: &targetUserID,
		Description:  reason,
		Reason:       reason,
		CreatedAt:    time.Now(),
	}
	s.DB.Create(&logEntry)
}

// GetWallet gets user's wallet
func (s *SimpleAPI) GetWallet(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		log.Printf("❌ GetWallet: Invalid user ID format: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	log.Printf("🔍 GetWallet: Looking for wallet for user: %s", userID)

	var wallet domain.Wallet
	err = s.DB.Where("user_id = ? AND is_deleted = false", userID).First(&wallet).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Printf("📝 GetWallet: No wallet found for user %s, creating new wallet", userID)

			// Create wallet if not exists
			wallet = domain.Wallet{
				ID:       uuid.New(),
				UserID:   userID,
				Balance:  0.0,
				Currency: "LYD",
				Status:   "active",
			}

			if createErr := s.DB.Create(&wallet).Error; createErr != nil {
				log.Printf("❌ GetWallet: Failed to create wallet for user %s: %v", userID, createErr)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create wallet"})
				return
			}

			log.Printf("✅ GetWallet: Successfully created wallet for user %s", userID)
		} else {
			log.Printf("❌ GetWallet: Database error getting wallet for user %s: %v", userID, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get wallet"})
			return
		}
	}

	log.Printf("✅ GetWallet: Successfully retrieved wallet for user %s, balance: %.2f %s", userID, wallet.Balance, wallet.Currency)
	c.JSON(http.StatusOK, wallet)
}

// UpdateWallet updates user's wallet
func (s *SimpleAPI) UpdateWallet(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req struct {
		Balance  float64 `json:"balance"`
		Currency string  `json:"currency"`
		Status   string  `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var wallet domain.Wallet
	if err := s.DB.First(&wallet, "user_id = ?", userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Wallet not found"})
		return
	}

	// Update wallet fields
	if req.Balance != 0 {
		wallet.Balance = req.Balance
	}
	if req.Currency != "" {
		wallet.Currency = req.Currency
	}
	if req.Status != "" {
		wallet.Status = req.Status
	}

	if err := s.DB.Save(&wallet).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update wallet"})
		return
	}

	c.JSON(http.StatusOK, wallet)
}

// GetProducts gets all products with optional filtering, pagination, and search
// Forever Plan: Enhanced endpoint to support Flutter product requirements
func (s *SimpleAPI) GetProducts(c *gin.Context) {
	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Build query with filters - Simplified for stability
	query := s.DB.Model(&domain.Product{}).Where("is_deleted = false")

	// Apply filters
	if categoryId := c.Query("category_id"); categoryId != "" {
		// Try category_id (UUID) first, then category (string)
		if categoryUUID, err := uuid.Parse(categoryId); err == nil {
			query = query.Where("category_id = ?", categoryUUID)
		} else {
			query = query.Where("category = ?", categoryId)
		}
	}

	if sellerId := c.Query("seller_id"); sellerId != "" {
		if userID, err := uuid.Parse(sellerId); err == nil {
			// Check both seller_id and user_id for compatibility
			query = query.Where("seller_id = ? OR user_id = ?", userID, userID)
		}
	}

	if isFeaturedStr := c.Query("is_featured"); isFeaturedStr == "true" {
		query = query.Where("is_featured = true")
	}

	if searchQuery := c.Query("search"); searchQuery != "" {
		searchPattern := "%" + searchQuery + "%"
		query = query.Where(
			"name ILIKE ? OR description ILIKE ?",
			searchPattern, searchPattern,
		)
	}

	if minPriceStr := c.Query("min_price"); minPriceStr != "" {
		if minPrice, err := strconv.ParseFloat(minPriceStr, 64); err == nil {
			query = query.Where("price >= ?", minPrice)
		}
	}

	if maxPriceStr := c.Query("max_price"); maxPriceStr != "" {
		if maxPrice, err := strconv.ParseFloat(maxPriceStr, 64); err == nil {
			query = query.Where("price <= ?", maxPrice)
		}
	}

	// Apply sorting
	sortBy := c.DefaultQuery("sort_by", "created_at")
	order := c.DefaultQuery("order", "desc")

	switch sortBy {
	case "price":
		query = query.Order("price " + order)
	case "name":
		query = query.Order("name " + order)
	case "updated_at":
		query = query.Order("updated_at " + order)
	default:
		query = query.Order("created_at " + order)
	}

	// Count total for pagination
	// Get products with pagination (without count for now to avoid errors)
	var products []domain.Product
	if err := query.Offset(offset).Limit(limit).Find(&products).Error; err != nil {
		log.Printf("❌ Database fetch error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to fetch products",
			"details": err.Error(),
		})
		return
	}

	// Try to count with a fresh simple query - if it fails, use approximate count
	var total int64 = int64(len(products)) // Fallback value
	simpleCountQuery := s.DB.Model(&domain.Product{}).Where("is_deleted = false")
	if err := simpleCountQuery.Count(&total).Error; err != nil {
		log.Printf("⚠️ Database count failed, using products length: %v", err)
		total = int64(len(products))
	}

	// Calculate pagination metadata
	totalPages := (int(total) + limit - 1) / limit
	hasMore := len(products) == limit // Simple heuristic: if we got full page, there might be more

	// Return paginated response compatible with Flutter ProductsResult
	c.JSON(http.StatusOK, gin.H{
		"products": products,
		"pagination": gin.H{
			"current_page": page,
			"total_pages":  totalPages,
			"total_count":  total,
			"has_more":     hasMore,
			"limit":        limit,
		},
	})
}

// GetProductsByCategory gets products by category with pagination
// Forever Plan: Dedicated endpoint for category-based product listing
func (s *SimpleAPI) GetProductsByCategory(c *gin.Context) {
	categoryId := c.Param("category_id")
	if categoryId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID required"})
		return
	}

	// Parse pagination
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := (page - 1) * limit

	// Query products by category
	query := s.DB.Model(&domain.Product{}).
		Where("main_category_id = ? AND is_active = true", categoryId)

	// Apply additional filters
	if isFeaturedStr := c.Query("is_featured"); isFeaturedStr == "true" {
		query = query.Where("is_featured = true")
	}

	var total int64
	query.Count(&total)

	var products []domain.Product
	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&products).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch category products"})
		return
	}

	// Return response
	c.JSON(http.StatusOK, products)
}

// SearchProducts searches products by query
// Forever Plan: Dedicated search endpoint for product search functionality
func (s *SimpleAPI) SearchProducts(c *gin.Context) {
	searchQuery := c.Query("q")
	if searchQuery == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query required"})
		return
	}

	// Parse pagination
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := (page - 1) * limit

	// Search in name and description
	searchPattern := "%" + searchQuery + "%"
	query := s.DB.Model(&domain.Product{}).
		Where("(name_en ILIKE ? OR name_ar ILIKE ? OR description_en ILIKE ?) AND is_active = true",
			searchPattern, searchPattern, searchPattern)

	// Apply category filter if provided
	if categoryId := c.Query("category_id"); categoryId != "" {
		query = query.Where("category = ?", categoryId)
	}

	var total int64
	query.Count(&total)

	var products []domain.Product
	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&products).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search products"})
		return
	}

	totalPages := (int(total) + limit - 1) / limit
	hasMore := page < totalPages

	// Return search results
	c.JSON(http.StatusOK, gin.H{
		"products":     products,
		"search_query": searchQuery,
		"pagination": gin.H{
			"current_page": page,
			"total_pages":  totalPages,
			"total_count":  total,
			"has_more":     hasMore,
			"limit":        limit,
		},
	})
}

// CreateProduct creates a new product for a user
func (s *SimpleAPI) CreateProduct(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req struct {
		Name        string  `json:"name" binding:"required"`
		Description string  `json:"description"`
		Price       float64 `json:"price" binding:"required,gt=0"`
		Category    string  `json:"category"`
		Brand       string  `json:"brand"`
		Model       string  `json:"model"`
		Year        *int    `json:"year"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set defaults for optional fields
	isActive := true
	categoryRootID := 1 // Default category

	product := domain.Product{
		ID:             uuid.New(),
		UserID:         &userID,
		SellerID:       &userID, // Set seller_id same as user_id
		Name:           req.Name,
		Description:    &req.Description,
		Price:          req.Price,
		CategoryRootID: &categoryRootID, // Now a pointer
		IsActive:       &isActive,
	}

	if err := s.DB.Create(&product).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create product"})
		return
	}

	c.JSON(http.StatusCreated, product)
}

// GetUserProducts gets products for a user
func (s *SimpleAPI) GetUserProducts(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var products []domain.Product
	if err := s.DB.Where("user_id = ? AND is_active = true", userID).Find(&products).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch user products"})
		return
	}

	c.JSON(http.StatusOK, products)
}

// CreateWalletTransaction creates a new wallet transaction
func (s *SimpleAPI) CreateWalletTransaction(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("user_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req struct {
		Amount      float64 `json:"amount" binding:"required"`
		Type        string  `json:"type" binding:"required,oneof=deposit withdraw"`
		Description string  `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user's wallet
	var wallet domain.Wallet
	if err := s.DB.Where("user_id = ?", userID).First(&wallet).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Wallet not found"})
		return
	}

	// Store balance before transaction
	balanceBefore := wallet.Balance

	// Create transaction record
	transaction := domain.WalletTransaction{
		ID:            uuid.New(),
		UserID:        userID,
		WalletID:      wallet.ID,
		Type:          req.Type,
		Amount:        req.Amount,
		Currency:      wallet.Currency,
		BalanceBefore: balanceBefore,
		Description:   &req.Description,
		Status:        "completed",
	}

	// Update wallet balance
	if req.Type == "deposit" {
		wallet.Balance += req.Amount
	} else if req.Type == "withdraw" {
		if wallet.Balance < req.Amount {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Insufficient balance"})
			return
		}
		wallet.Balance -= req.Amount
	}

	// Set balance after transaction
	transaction.BalanceAfter = wallet.Balance

	// Start database transaction
	tx := s.DB.Begin()

	// Create transaction record
	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction"})
		return
	}

	// Update wallet in database
	if err := tx.Save(&wallet).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update wallet"})
		return
	}

	// Commit transaction
	tx.Commit()

	c.JSON(http.StatusCreated, gin.H{
		"transaction": transaction,
		"wallet":      wallet,
	})
}

// GetWalletTransactions gets user's wallet transaction history with pagination
// Forever Plan: Simple endpoint to retrieve transaction history
func (s *SimpleAPI) GetWalletTransactions(c *gin.Context) {
	// Get user ID from JWT context (authenticated user)
	userIDStr, exists := c.Get("user_id")
	if !exists {
		log.Printf("❌ GetWalletTransactions: User not authenticated")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		log.Printf("❌ GetWalletTransactions: Invalid user ID format: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	log.Printf("🔍 GetWalletTransactions: Getting transactions for user: %s", userID)

	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Calculate offset
	offset := (page - 1) * limit

	log.Printf("📄 GetWalletTransactions: Pagination - page: %d, limit: %d, offset: %d", page, limit, offset)

	// Get user's wallet first with error handling
	var wallet domain.Wallet
	err = s.DB.Where("user_id = ? AND is_deleted = false", userID).First(&wallet).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Printf("📝 GetWalletTransactions: No wallet found for user %s, returning empty transactions", userID)
			// Return empty transactions for users without wallets
			c.JSON(http.StatusOK, gin.H{
				"transactions": []domain.WalletTransaction{},
				"pagination": gin.H{
					"page":        page,
					"limit":       limit,
					"total":       0,
					"total_pages": 0,
					"has_next":    false,
					"has_prev":    false,
				},
				"wallet_balance":  0.0,
				"wallet_currency": "LYD",
			})
			return
		} else {
			log.Printf("❌ GetWalletTransactions: Database error getting wallet for user %s: %v", userID, err)
			// Return empty transactions instead of error to prevent app crashes
			c.JSON(http.StatusOK, gin.H{
				"transactions": []domain.WalletTransaction{},
				"pagination": gin.H{
					"page":        page,
					"limit":       limit,
					"total":       0,
					"total_pages": 0,
					"has_next":    false,
					"has_prev":    false,
				},
				"wallet_balance":  0.0,
				"wallet_currency": "LYD",
			})
			return
		}
	}

	// Count total transactions with error handling
	var total int64
	countErr := s.DB.Model(&domain.WalletTransaction{}).
		Where("user_id = ? AND is_deleted = false", userID).
		Count(&total).Error

	if countErr != nil {
		log.Printf("⚠️ GetWalletTransactions: Error counting transactions for user %s: %v", userID, countErr)
		// Continue with total = 0 instead of failing
		total = 0
	}

	// Get transactions for this user's wallet with error handling
	var transactions []domain.WalletTransaction
	queryErr := s.DB.Where("user_id = ? AND is_deleted = false", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&transactions).Error

	if queryErr != nil {
		log.Printf("❌ GetWalletTransactions: Database error getting transactions for user %s: %v", userID, queryErr)
		// Return empty transactions instead of error
		transactions = []domain.WalletTransaction{}
	}

	// Calculate pagination metadata
	totalPages := 0
	if total > 0 {
		totalPages = (int(total) + limit - 1) / limit
	}
	hasNext := page < totalPages
	hasPrev := page > 1

	log.Printf("✅ GetWalletTransactions: Successfully retrieved %d transactions for user %s (total: %d)", len(transactions), userID, total)

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"transactions": transactions,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": totalPages,
			"has_next":    hasNext,
			"has_prev":    hasPrev,
		},
		"wallet_balance":  wallet.Balance,
		"wallet_currency": wallet.Currency,
	})
}

// GetProduct gets a specific product by ID
func (s *SimpleAPI) GetProduct(c *gin.Context) {
	productID, err := uuid.Parse(c.Param("product_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
		return
	}

	var product domain.Product
	if err := s.DB.First(&product, "id = ? AND is_deleted = false", productID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
		return
	}

	c.JSON(http.StatusOK, product)
}

// UpdateProduct updates a product
func (s *SimpleAPI) UpdateProduct(c *gin.Context) {
	productID, err := uuid.Parse(c.Param("product_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
		return
	}

	// Get current user from JWT context
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req struct {
		Name        string  `json:"name"`
		Description string  `json:"description"`
		Price       float64 `json:"price"`
		Status      string  `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var product domain.Product
	if err := s.DB.First(&product, "id = ? AND is_deleted = false", productID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
		return
	}

	// Check if user owns this product
	if product.UserID.String() != currentUserID {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only update your own products"})
		return
	}

	// Update fields
	if req.Name != "" {
		product.Name = req.Name
	}
	if req.Description != "" {
		*product.Description = req.Description
	}
	if req.Price > 0 {
		product.Price = req.Price
	}
	// Note: Status field removed from Product model - using IsActive instead
	// if req.Status != "" {
	//     product.Status = req.Status
	// }

	if err := s.DB.Save(&product).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update product"})
		return
	}

	c.JSON(http.StatusOK, product)
}

// DeleteProduct soft deletes a product
func (s *SimpleAPI) DeleteProduct(c *gin.Context) {
	productID, err := uuid.Parse(c.Param("product_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
		return
	}

	// Get current user from JWT context
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var product domain.Product
	if err := s.DB.Where("id = ? AND is_deleted = false", productID).First(&product).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
		return
	}

	// Check if user owns this product
	if product.UserID.String() != currentUserID {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only delete your own products"})
		return
	}

	// Soft delete
	product.IsDeleted = true
	if err := s.DB.Save(&product).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete product"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Product deleted successfully"})
}

// ============================================================================
// Orders API Handlers - Forever Plan
// ============================================================================

// CreateOrder creates a new order with items
func (s *SimpleAPI) CreateOrder(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req domain.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Start transaction
	tx := s.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Calculate total amount and validate products
	var totalAmount float64
	var orderItems []domain.OrderItem

	for _, item := range req.Items {
		var product domain.Product
		if err := tx.Where("id = ? AND is_deleted = false", item.ProductID).First(&product).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{"error": "Product not found: " + item.ProductID.String()})
			return
		}

		orderItem := domain.OrderItem{
			ProductID:  item.ProductID,
			Quantity:   item.Quantity,
			UnitPrice:  product.Price,
			TotalPrice: product.Price * float64(item.Quantity),
		}
		orderItems = append(orderItems, orderItem)
		totalAmount += orderItem.TotalPrice
	}

	// Create order
	order := domain.Order{
		UserID:          userID,
		Status:          domain.OrderStatusPending,
		TotalAmount:     totalAmount,
		Currency:        "LYD",
		ShippingAddress: req.ShippingAddress,
		BillingAddress:  req.BillingAddress,
		PaymentMethod:   req.PaymentMethod,
		PaymentStatus:   domain.PaymentStatusPending,
		Notes:           req.Notes,
	}

	if err := tx.Create(&order).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order"})
		return
	}

	// Create order items
	for i := range orderItems {
		orderItems[i].OrderID = order.ID
		if err := tx.Create(&orderItems[i]).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order items"})
			return
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit order"})
		return
	}

	// Load order with items and products for response
	var responseOrder domain.Order
	if err := s.DB.Preload("OrderItems.Product").Where("id = ?", order.ID).First(&responseOrder).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load order details"})
		return
	}

	c.JSON(http.StatusCreated, responseOrder)
}

// GetUserOrders gets orders for the authenticated user
func (s *SimpleAPI) GetUserOrders(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := (page - 1) * limit

	var orders []domain.Order
	var total int64

	// Get total count
	if err := s.DB.Model(&domain.Order{}).Where("user_id = ? AND is_deleted = false", userID).Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count orders"})
		return
	}

	// Get orders with items and products
	if err := s.DB.Preload("OrderItems.Product").Where("user_id = ? AND is_deleted = false", userID).
		Order("created_at DESC").Offset(offset).Limit(limit).Find(&orders).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch orders"})
		return
	}

	// Create paginated response
	response := gin.H{
		"orders": orders,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetOrder gets a specific order by ID
func (s *SimpleAPI) GetOrder(c *gin.Context) {
	orderID, err := uuid.Parse(c.Param("order_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var order domain.Order
	if err := s.DB.Preload("OrderItems.Product").Preload("User").
		Where("id = ? AND is_deleted = false", orderID).First(&order).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}

	// Check if user owns this order (or is admin)
	userRole, _ := c.Get("user_role")
	if order.UserID.String() != userIDStr.(string) && userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// UpdateOrder updates an order
func (s *SimpleAPI) UpdateOrder(c *gin.Context) {
	orderID, err := uuid.Parse(c.Param("order_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req domain.UpdateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var order domain.Order
	if err := s.DB.Where("id = ? AND is_deleted = false", orderID).First(&order).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}

	// Check permissions
	userRole, _ := c.Get("user_role")
	isAdmin := userRole == "admin"
	isOwner := order.UserID.String() == userIDStr.(string)

	if !isAdmin && !isOwner {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Update fields
	if req.Status != nil {
		// Only admin can change status to certain values
		if *req.Status == domain.OrderStatusConfirmed || *req.Status == domain.OrderStatusProcessing ||
			*req.Status == domain.OrderStatusShipped || *req.Status == domain.OrderStatusDelivered {
			if !isAdmin {
				c.JSON(http.StatusForbidden, gin.H{"error": "Only admin can set this status"})
				return
			}
		}
		order.Status = *req.Status
	}

	if req.ShippingAddress != nil {
		order.ShippingAddress = req.ShippingAddress
	}

	if req.BillingAddress != nil {
		order.BillingAddress = req.BillingAddress
	}

	if req.PaymentMethod != nil {
		order.PaymentMethod = req.PaymentMethod
	}

	if req.PaymentStatus != nil && isAdmin {
		order.PaymentStatus = *req.PaymentStatus
	}

	if req.Notes != nil {
		order.Notes = req.Notes
	}

	if err := s.DB.Save(&order).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update order"})
		return
	}

	// Load updated order with relations
	var updatedOrder domain.Order
	if err := s.DB.Preload("OrderItems.Product").Where("id = ?", orderID).First(&updatedOrder).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load updated order"})
		return
	}

	c.JSON(http.StatusOK, updatedOrder)
}

// CancelOrder cancels an order (soft delete or status change)
func (s *SimpleAPI) CancelOrder(c *gin.Context) {
	orderID, err := uuid.Parse(c.Param("order_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var order domain.Order
	if err := s.DB.Where("id = ? AND is_deleted = false", orderID).First(&order).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}

	// Check if user owns this order
	if order.UserID.String() != userIDStr.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only cancel your own orders"})
		return
	}

	// Check if order can be cancelled
	if order.Status == domain.OrderStatusDelivered || order.Status == domain.OrderStatusCancelled {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order cannot be cancelled"})
		return
	}

	// Update status to cancelled
	order.Status = domain.OrderStatusCancelled
	if err := s.DB.Save(&order).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel order"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Order cancelled successfully"})
}

// GetAllOrders gets all orders (admin only)
func (s *SimpleAPI) GetAllOrders(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := (page - 1) * limit

	var orders []domain.Order
	var total int64

	// Build query
	query := s.DB.Model(&domain.Order{}).Where("is_deleted = false")

	// Add filters
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	if userID := c.Query("user_id"); userID != "" {
		if _, err := uuid.Parse(userID); err == nil {
			query = query.Where("user_id = ?", userID)
		}
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count orders"})
		return
	}

	// Get orders
	if err := query.Preload("OrderItems.Product").Preload("User").
		Order("created_at DESC").Offset(offset).Limit(limit).Find(&orders).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch orders"})
		return
	}

	// Create paginated response
	response := gin.H{
		"orders": orders,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ============================================================================
// Seller Management API Handlers - Forever Plan
// ============================================================================

// CreateSeller creates a new seller profile for the authenticated user
func (s *SimpleAPI) CreateSeller(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req domain.CreateSellerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user already has a seller profile
	var existingSeller domain.Seller
	if err := s.DB.Where("user_id = ? AND is_deleted = false", userID).First(&existingSeller).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Seller profile already exists"})
		return
	}

	// Create seller
	seller := domain.Seller{
		UserID:          userID,
		BusinessName:    req.BusinessName,
		BusinessType:    req.BusinessType,
		TaxNumber:       req.TaxNumber,
		BusinessAddress: req.BusinessAddress,
		PhoneNumber:     req.PhoneNumber,
		Email:           req.Email,
		Website:         req.Website,
		Description:     req.Description,
		Status:          domain.SellerStatusPending,
		IsVerified:      false,
		CommissionRate:  5.0, // Default commission rate
	}

	if err := s.DB.Create(&seller).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create seller profile"})
		return
	}

	// Load seller with user data
	var responseSeller domain.Seller
	if err := s.DB.Preload("User").Where("id = ?", seller.ID).First(&responseSeller).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load seller details"})
		return
	}

	c.JSON(http.StatusCreated, responseSeller)
}

// GetSeller gets seller profile for the authenticated user
func (s *SimpleAPI) GetSeller(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var seller domain.Seller
	if err := s.DB.Preload("User").Preload("Products").
		Where("user_id = ? AND is_deleted = false", userID).First(&seller).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Seller profile not found"})
		return
	}

	c.JSON(http.StatusOK, seller)
}

// GetSellerByID gets a specific seller by ID (public endpoint)
func (s *SimpleAPI) GetSellerByID(c *gin.Context) {
	sellerID, err := uuid.Parse(c.Param("seller_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid seller ID"})
		return
	}

	var seller domain.Seller
	if err := s.DB.Preload("User").Preload("Products", "is_deleted = false AND status = 'active'").
		Where("id = ? AND is_deleted = false AND status = 'approved'", sellerID).First(&seller).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Seller not found"})
		return
	}

	c.JSON(http.StatusOK, seller)
}

// UpdateSeller updates seller profile
func (s *SimpleAPI) UpdateSeller(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req domain.UpdateSellerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var seller domain.Seller
	if err := s.DB.Where("user_id = ? AND is_deleted = false", userID).First(&seller).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Seller profile not found"})
		return
	}

	// Update fields
	if req.BusinessName != nil {
		seller.BusinessName = *req.BusinessName
	}
	if req.BusinessType != nil {
		seller.BusinessType = *req.BusinessType
	}
	if req.TaxNumber != nil {
		seller.TaxNumber = req.TaxNumber
	}
	if req.BusinessAddress != nil {
		seller.BusinessAddress = req.BusinessAddress
	}
	if req.PhoneNumber != nil {
		seller.PhoneNumber = req.PhoneNumber
	}
	if req.Email != nil {
		seller.Email = req.Email
	}
	if req.Website != nil {
		seller.Website = req.Website
	}
	if req.Description != nil {
		seller.Description = req.Description
	}

	// Only admin can update commission rate
	userRole, _ := c.Get("user_role")
	if req.CommissionRate != nil && userRole == "admin" {
		seller.CommissionRate = *req.CommissionRate
	}

	if err := s.DB.Save(&seller).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update seller profile"})
		return
	}

	// Load updated seller with relations
	var updatedSeller domain.Seller
	if err := s.DB.Preload("User").Where("id = ?", seller.ID).First(&updatedSeller).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load updated seller"})
		return
	}

	c.JSON(http.StatusOK, updatedSeller)
}

// GetAllSellers gets all sellers (admin only)
func (s *SimpleAPI) GetAllSellers(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := (page - 1) * limit

	var sellers []domain.Seller
	var total int64

	// Build query
	query := s.DB.Model(&domain.Seller{}).Where("is_deleted = false")

	// Add filters
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	if verified := c.Query("verified"); verified != "" {
		if verified == "true" {
			query = query.Where("is_verified = true")
		} else if verified == "false" {
			query = query.Where("is_verified = false")
		}
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count sellers"})
		return
	}

	// Get sellers
	if err := query.Preload("User").
		Order("created_at DESC").Offset(offset).Limit(limit).Find(&sellers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sellers"})
		return
	}

	// Create paginated response
	response := gin.H{
		"sellers": sellers,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

// UpdateSellerStatus updates seller status (admin only)
func (s *SimpleAPI) UpdateSellerStatus(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	sellerID, err := uuid.Parse(c.Param("seller_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid seller ID"})
		return
	}

	var req domain.UpdateSellerStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var seller domain.Seller
	if err := s.DB.Where("id = ? AND is_deleted = false", sellerID).First(&seller).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Seller not found"})
		return
	}

	// Update status and verification
	seller.Status = req.Status
	if req.IsVerified != nil {
		seller.IsVerified = *req.IsVerified
	}

	if err := s.DB.Save(&seller).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update seller status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Seller status updated successfully"})
}

// UploadSellerDocument uploads verification document for seller
func (s *SimpleAPI) UploadSellerDocument(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req domain.UploadDocumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get seller profile
	var seller domain.Seller
	if err := s.DB.Where("user_id = ? AND is_deleted = false", userID).First(&seller).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Seller profile not found"})
		return
	}

	// Create document record
	document := domain.SellerDocument{
		SellerID:     seller.ID,
		DocumentType: req.DocumentType,
		DocumentURL:  req.DocumentURL,
		Status:       domain.DocumentStatusPending,
	}

	if err := s.DB.Create(&document).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload document"})
		return
	}

	c.JSON(http.StatusCreated, document)
}

// GetSellerDocuments gets documents for seller
func (s *SimpleAPI) GetSellerDocuments(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get seller profile
	var seller domain.Seller
	if err := s.DB.Where("user_id = ? AND is_deleted = false", userID).First(&seller).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Seller profile not found"})
		return
	}

	var documents []domain.SellerDocument
	if err := s.DB.Where("seller_id = ? AND is_deleted = false", seller.ID).
		Order("uploaded_at DESC").Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch documents"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"documents": documents})
}

// GetPublicSellers gets approved sellers for public viewing
func (s *SimpleAPI) GetPublicSellers(c *gin.Context) {
	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := (page - 1) * limit

	var sellers []domain.Seller
	var total int64

	// Get only approved and verified sellers
	query := s.DB.Model(&domain.Seller{}).Where("is_deleted = false AND status = 'approved' AND is_verified = true")

	// Add search filter
	if search := c.Query("search"); search != "" {
		query = query.Where("business_name ILIKE ?", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count sellers"})
		return
	}

	// Get sellers
	if err := query.Select("id, user_id, business_name, business_type, description, rating, total_sales, created_at").
		Order("rating DESC, total_sales DESC").Offset(offset).Limit(limit).Find(&sellers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sellers"})
		return
	}

	// Create paginated response
	response := gin.H{
		"sellers": sellers,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ============================================================================
// Categories API Handlers - Forever Plan
// ============================================================================

// CreateCategory creates a new category (admin only)
func (s *SimpleAPI) CreateCategory(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	var req domain.CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if slug already exists
	var existingCategory domain.Category
	if err := s.DB.Where("slug = ? AND is_deleted = false", req.Slug).First(&existingCategory).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Category slug already exists"})
		return
	}

	// Validate parent category if provided
	if req.ParentID != nil {
		var parent domain.Category
		if err := s.DB.Where("id = ? AND is_deleted = false", *req.ParentID).First(&parent).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Parent category not found"})
			return
		}
		// Prevent too deep nesting (max 4 levels)
		if parent.Level >= domain.CategoryLevelFour {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Maximum category depth exceeded"})
			return
		}
	}

	// Create category
	category := domain.Category{
		Name:        req.Name,
		Slug:        req.Slug,
		Description: req.Description,
		ImageURL:    req.ImageURL,
		ParentID:    req.ParentID,
	}

	if req.SortOrder != nil {
		category.SortOrder = *req.SortOrder
	}

	if err := s.DB.Create(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create category"})
		return
	}

	// Load category with parent
	var responseCategory domain.Category
	if err := s.DB.Preload("Parent").Where("id = ?", category.ID).First(&responseCategory).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load category details"})
		return
	}

	c.JSON(http.StatusCreated, responseCategory)
}

// GetCategories gets all categories with optional filtering
func (s *SimpleAPI) GetCategories(c *gin.Context) {
	var categories []domain.Category

	// Build query
	query := s.DB.Where("is_deleted = false AND is_active = true")

	// Filter by parent ID
	if parentID := c.Query("parent_id"); parentID != "" {
		if parentID == "null" || parentID == "" {
			query = query.Where("parent_id IS NULL")
		} else {
			if _, err := uuid.Parse(parentID); err == nil {
				query = query.Where("parent_id = ?", parentID)
			}
		}
	}

	// Filter by level
	if level := c.Query("level"); level != "" {
		if levelInt, err := strconv.Atoi(level); err == nil {
			query = query.Where("level = ?", levelInt)
		}
	}

	// Search by name
	if search := c.Query("search"); search != "" {
		query = query.Where("name ILIKE ?", "%"+search+"%")
	}

	// Order by sort order and name
	if err := query.Order("sort_order ASC, name ASC").Find(&categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"categories": categories})
}

// GetCategoryTree gets categories in hierarchical tree structure
func (s *SimpleAPI) GetCategoryTree(c *gin.Context) {
	var allCategories []domain.Category

	// Get all active categories
	if err := s.DB.Where("is_deleted = false AND is_active = true").
		Order("level ASC, sort_order ASC, name ASC").Find(&allCategories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	// Build tree structure
	categoryMap := make(map[uuid.UUID]*domain.CategoryTreeResponse)
	var rootCategories []domain.CategoryTreeResponse

	// First pass: create all category responses
	for _, cat := range allCategories {
		categoryMap[cat.ID] = &domain.CategoryTreeResponse{
			ID:           cat.ID,
			Name:         cat.Name,
			Slug:         cat.Slug,
			Description:  cat.Description,
			ImageURL:     cat.ImageURL,
			Level:        cat.Level,
			SortOrder:    cat.SortOrder,
			ProductCount: cat.ProductCount,
			Children:     []domain.CategoryTreeResponse{},
		}
	}

	// Second pass: build hierarchy
	for _, cat := range allCategories {
		if cat.ParentID == nil {
			// Root category
			rootCategories = append(rootCategories, *categoryMap[cat.ID])
		} else {
			// Child category
			if parent, exists := categoryMap[*cat.ParentID]; exists {
				parent.Children = append(parent.Children, *categoryMap[cat.ID])
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{"categories": rootCategories})
}

// GetCategory gets a specific category by ID or slug
func (s *SimpleAPI) GetCategory(c *gin.Context) {
	identifier := c.Param("category_id")

	var category domain.Category
	var err error

	// Try to parse as UUID first, if it fails, treat as slug
	if _, uuidErr := uuid.Parse(identifier); uuidErr == nil {
		err = s.DB.Preload("Parent").Preload("Children", "is_deleted = false AND is_active = true").
			Where("id = ? AND is_deleted = false", identifier).First(&category).Error
	} else {
		err = s.DB.Preload("Parent").Preload("Children", "is_deleted = false AND is_active = true").
			Where("slug = ? AND is_deleted = false", identifier).First(&category).Error
	}

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	c.JSON(http.StatusOK, category)
}

// UpdateCategory updates a category (admin only)
func (s *SimpleAPI) UpdateCategory(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	categoryID, err := uuid.Parse(c.Param("category_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var req domain.UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var category domain.Category
	if err := s.DB.Where("id = ? AND is_deleted = false", categoryID).First(&category).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Check if slug already exists (if changing slug)
	if req.Slug != nil && *req.Slug != category.Slug {
		var existingCategory domain.Category
		if err := s.DB.Where("slug = ? AND is_deleted = false AND id != ?", *req.Slug, categoryID).First(&existingCategory).Error; err == nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Category slug already exists"})
			return
		}
	}

	// Validate parent category if changing parent
	if req.ParentID != nil {
		// Prevent self-reference
		if *req.ParentID == categoryID {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Category cannot be its own parent"})
			return
		}

		var parent domain.Category
		if err := s.DB.Where("id = ? AND is_deleted = false", *req.ParentID).First(&parent).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Parent category not found"})
			return
		}
	}

	// Update fields
	if req.Name != nil {
		category.Name = *req.Name
	}
	if req.Slug != nil {
		category.Slug = *req.Slug
	}
	if req.Description != nil {
		category.Description = req.Description
	}
	if req.ImageURL != nil {
		category.ImageURL = req.ImageURL
	}
	if req.ParentID != nil {
		category.ParentID = req.ParentID
		// Recalculate level
		if req.ParentID != nil {
			var parent domain.Category
			if err := s.DB.Where("id = ?", *req.ParentID).First(&parent).Error; err == nil {
				category.Level = parent.Level + 1
			}
		} else {
			category.Level = domain.CategoryLevelRoot
		}
	}
	if req.SortOrder != nil {
		category.SortOrder = *req.SortOrder
	}
	if req.IsActive != nil {
		category.IsActive = *req.IsActive
	}

	if err := s.DB.Save(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update category"})
		return
	}

	// Load updated category with relations
	var updatedCategory domain.Category
	if err := s.DB.Preload("Parent").Where("id = ?", categoryID).First(&updatedCategory).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load updated category"})
		return
	}

	c.JSON(http.StatusOK, updatedCategory)
}

// DeleteCategory soft deletes a category (admin only)
func (s *SimpleAPI) DeleteCategory(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	categoryID, err := uuid.Parse(c.Param("category_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var category domain.Category
	if err := s.DB.Where("id = ? AND is_deleted = false", categoryID).First(&category).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Check if category has children
	var childCount int64
	if err := s.DB.Model(&domain.Category{}).Where("parent_id = ? AND is_deleted = false", categoryID).Count(&childCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check category children"})
		return
	}

	if childCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete category with children. Please delete or move children first."})
		return
	}

	// Check if category has products
	var productCount int64
	if err := s.DB.Model(&domain.ProductCategory{}).Where("category_id = ?", categoryID).Count(&productCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check category products"})
		return
	}

	if productCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete category with products. Please move or remove products first."})
		return
	}

	// Soft delete
	category.IsDeleted = true
	if err := s.DB.Save(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete category"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Category deleted successfully"})
}

// AssignProductToCategory assigns a product to a category
func (s *SimpleAPI) AssignProductToCategory(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	productID, err := uuid.Parse(c.Param("product_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
		return
	}

	categoryID, err := uuid.Parse(c.Param("category_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	// Check if product exists and user owns it
	var product domain.Product
	if err := s.DB.Where("id = ? AND is_deleted = false", productID).First(&product).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
		return
	}

	// Check ownership or admin
	userRole, _ := c.Get("user_role")
	if product.UserID.String() != userIDStr.(string) && userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Check if category exists
	var category domain.Category
	if err := s.DB.Where("id = ? AND is_deleted = false AND is_active = true", categoryID).First(&category).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Check if assignment already exists
	var existingAssignment domain.ProductCategory
	if err := s.DB.Where("product_id = ? AND category_id = ?", productID, categoryID).First(&existingAssignment).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Product already assigned to this category"})
		return
	}

	// Create assignment
	assignment := domain.ProductCategory{
		ProductID:  productID,
		CategoryID: categoryID,
	}

	if err := s.DB.Create(&assignment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign product to category"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "Product assigned to category successfully"})
}

// RemoveProductFromCategory removes a product from a category
func (s *SimpleAPI) RemoveProductFromCategory(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	productID, err := uuid.Parse(c.Param("product_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
		return
	}

	categoryID, err := uuid.Parse(c.Param("category_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	// Check if product exists and user owns it
	var product domain.Product
	if err := s.DB.Where("id = ? AND is_deleted = false", productID).First(&product).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
		return
	}

	// Check ownership or admin
	userRole, _ := c.Get("user_role")
	if product.UserID.String() != userIDStr.(string) && userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Find and delete assignment
	var assignment domain.ProductCategory
	if err := s.DB.Where("product_id = ? AND category_id = ?", productID, categoryID).First(&assignment).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product not assigned to this category"})
		return
	}

	if err := s.DB.Delete(&assignment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove product from category"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Product removed from category successfully"})
}

// ============================================================================
// Vehicle Data API Handlers - Forever Plan
// ============================================================================

// GetVehicleBrands gets all vehicle brands
func (s *SimpleAPI) GetVehicleBrands(c *gin.Context) {
	var brands []domain.VehicleBrand

	// Build query
	query := s.DB.Where("is_deleted = false AND is_active = true")

	// Search filter
	if search := c.Query("search"); search != "" {
		query = query.Where("name ILIKE ? OR country ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Country filter
	if country := c.Query("country"); country != "" {
		query = query.Where("country = ?", country)
	}

	// Order by name
	if err := query.Order("name ASC").Find(&brands).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch vehicle brands"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"brands": brands})
}

// GetVehicleBrand gets a specific vehicle brand by ID or slug
func (s *SimpleAPI) GetVehicleBrand(c *gin.Context) {
	identifier := c.Param("brand_id")

	var brand domain.VehicleBrand
	var err error

	// Try to parse as UUID first, if it fails, treat as slug
	if _, uuidErr := uuid.Parse(identifier); uuidErr == nil {
		err = s.DB.Preload("Models", "is_deleted = false AND is_active = true").
			Where("id = ? AND is_deleted = false", identifier).First(&brand).Error
	} else {
		err = s.DB.Preload("Models", "is_deleted = false AND is_active = true").
			Where("slug = ? AND is_deleted = false", identifier).First(&brand).Error
	}

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Vehicle brand not found"})
		return
	}

	c.JSON(http.StatusOK, brand)
}

// CreateVehicleBrand creates a new vehicle brand (admin only)
func (s *SimpleAPI) CreateVehicleBrand(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	var req domain.CreateVehicleBrandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if name or slug already exists
	var existingBrand domain.VehicleBrand
	if err := s.DB.Where("(name = ? OR slug = ?) AND is_deleted = false", req.Name, req.Slug).First(&existingBrand).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Brand name or slug already exists"})
		return
	}

	// Create brand
	brand := domain.VehicleBrand{
		Name:        req.Name,
		Slug:        req.Slug,
		LogoURL:     req.LogoURL,
		Country:     req.Country,
		Website:     req.Website,
		Description: req.Description,
	}

	if err := s.DB.Create(&brand).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create vehicle brand"})
		return
	}

	c.JSON(http.StatusCreated, brand)
}

// UpdateVehicleBrand updates a vehicle brand (admin only)
func (s *SimpleAPI) UpdateVehicleBrand(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	brandID, err := uuid.Parse(c.Param("brand_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid brand ID"})
		return
	}

	var req domain.UpdateVehicleBrandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var brand domain.VehicleBrand
	if err := s.DB.Where("id = ? AND is_deleted = false", brandID).First(&brand).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Vehicle brand not found"})
		return
	}

	// Check for duplicate name/slug
	if req.Name != nil || req.Slug != nil {
		query := s.DB.Where("id != ? AND is_deleted = false", brandID)
		if req.Name != nil {
			query = query.Where("name = ?", *req.Name)
		}
		if req.Slug != nil {
			query = query.Where("slug = ?", *req.Slug)
		}

		var existingBrand domain.VehicleBrand
		if err := query.First(&existingBrand).Error; err == nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Brand name or slug already exists"})
			return
		}
	}

	// Update fields
	if req.Name != nil {
		brand.Name = *req.Name
	}
	if req.Slug != nil {
		brand.Slug = *req.Slug
	}
	if req.LogoURL != nil {
		brand.LogoURL = req.LogoURL
	}
	if req.Country != nil {
		brand.Country = req.Country
	}
	if req.Website != nil {
		brand.Website = req.Website
	}
	if req.Description != nil {
		brand.Description = req.Description
	}
	if req.IsActive != nil {
		brand.IsActive = *req.IsActive
	}

	if err := s.DB.Save(&brand).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update vehicle brand"})
		return
	}

	c.JSON(http.StatusOK, brand)
}

// GetVehicleModels gets vehicle models with optional brand filter
func (s *SimpleAPI) GetVehicleModels(c *gin.Context) {
	var models []domain.VehicleModel

	// Build query
	query := s.DB.Preload("Brand").Where("vehicle_models.is_deleted = false AND vehicle_models.is_active = true")

	// Brand filter
	if brandID := c.Query("brand_id"); brandID != "" {
		if _, err := uuid.Parse(brandID); err == nil {
			query = query.Where("brand_id = ?", brandID)
		}
	}

	// Type filter
	if vehicleType := c.Query("type"); vehicleType != "" {
		query = query.Where("type = ?", vehicleType)
	}

	// Body style filter
	if bodyStyle := c.Query("body_style"); bodyStyle != "" {
		query = query.Where("body_style = ?", bodyStyle)
	}

	// Fuel type filter
	if fuelType := c.Query("fuel_type"); fuelType != "" {
		query = query.Where("fuel_type = ?", fuelType)
	}

	// Year filter
	if year := c.Query("year"); year != "" {
		if yearInt, err := strconv.Atoi(year); err == nil {
			query = query.Where("(start_year IS NULL OR start_year <= ?) AND (end_year IS NULL OR end_year >= ?)", yearInt, yearInt)
		}
	}

	// Search filter
	if search := c.Query("search"); search != "" {
		query = query.Where("vehicle_models.name ILIKE ?", "%"+search+"%")
	}

	// Order by brand name and model name
	if err := query.Joins("JOIN vehicle_brands ON vehicle_brands.id = vehicle_models.brand_id").
		Order("vehicle_brands.name ASC, vehicle_models.name ASC").Find(&models).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch vehicle models"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"models": models})
}

// GetVehicleModel gets a specific vehicle model by ID
func (s *SimpleAPI) GetVehicleModel(c *gin.Context) {
	modelID, err := uuid.Parse(c.Param("model_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid model ID"})
		return
	}

	var model domain.VehicleModel
	if err := s.DB.Preload("Brand").Where("id = ? AND is_deleted = false", modelID).First(&model).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Vehicle model not found"})
		return
	}

	c.JSON(http.StatusOK, model)
}

// CreateVehicleModel creates a new vehicle model (admin only)
func (s *SimpleAPI) CreateVehicleModel(c *gin.Context) {
	// Check if user is admin
	userRole, _ := c.Get("user_role")
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	var req domain.CreateVehicleModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if brand exists
	var brand domain.VehicleBrand
	if err := s.DB.Where("id = ? AND is_deleted = false", req.BrandID).First(&brand).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Vehicle brand not found"})
		return
	}

	// Check if model slug already exists for this brand
	var existingModel domain.VehicleModel
	if err := s.DB.Where("brand_id = ? AND slug = ? AND is_deleted = false", req.BrandID, req.Slug).First(&existingModel).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Model slug already exists for this brand"})
		return
	}

	// Create model
	model := domain.VehicleModel{
		BrandID:     req.BrandID,
		Name:        req.Name,
		Slug:        req.Slug,
		Type:        req.Type,
		BodyStyle:   req.BodyStyle,
		FuelType:    req.FuelType,
		ImageURL:    req.ImageURL,
		Description: req.Description,
		StartYear:   req.StartYear,
		EndYear:     req.EndYear,
	}

	if err := s.DB.Create(&model).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create vehicle model"})
		return
	}

	// Load model with brand
	var responseModel domain.VehicleModel
	if err := s.DB.Preload("Brand").Where("id = ?", model.ID).First(&responseModel).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load model details"})
		return
	}

	c.JSON(http.StatusCreated, responseModel)
}

// CreateVehicle creates a new vehicle for the authenticated user
func (s *SimpleAPI) CreateVehicle(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req domain.CreateVehicleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate brand exists
	var brand domain.VehicleBrand
	if err := s.DB.Where("id = ? AND is_deleted = false", req.BrandID).First(&brand).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Vehicle brand not found"})
		return
	}

	// Validate model exists and belongs to brand
	var model domain.VehicleModel
	if err := s.DB.Where("id = ? AND brand_id = ? AND is_deleted = false", req.ModelID, req.BrandID).First(&model).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Vehicle model not found or doesn't belong to the specified brand"})
		return
	}

	// Check if VIN already exists (if provided)
	if req.VIN != nil && *req.VIN != "" {
		var existingVehicle domain.Vehicle
		if err := s.DB.Where("vin = ? AND is_deleted = false", *req.VIN).First(&existingVehicle).Error; err == nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Vehicle with this VIN already exists"})
			return
		}
	}

	// Create vehicle
	vehicle := domain.Vehicle{
		UserID:           userID,
		BrandID:          req.BrandID,
		ModelID:          req.ModelID,
		Year:             req.Year,
		Color:            req.Color,
		VIN:              req.VIN,
		LicensePlate:     req.LicensePlate,
		Mileage:          req.Mileage,
		TransmissionType: req.TransmissionType,
		EngineSize:       req.EngineSize,
		FuelType:         req.FuelType,
		PurchaseDate:     req.PurchaseDate,
		PurchasePrice:    req.PurchasePrice,
		CurrentValue:     req.CurrentValue,
		InsuranceExpiry:  req.InsuranceExpiry,
		MaintenanceDate:  req.MaintenanceDate,
		Notes:            req.Notes,
		ImageURLs:        req.ImageURLs,
	}

	if req.Condition != nil {
		vehicle.Condition = *req.Condition
	}

	if err := s.DB.Create(&vehicle).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create vehicle"})
		return
	}

	// Load vehicle with relations
	var responseVehicle domain.Vehicle
	if err := s.DB.Preload("Brand").Preload("Model").Preload("User").
		Where("id = ?", vehicle.ID).First(&responseVehicle).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load vehicle details"})
		return
	}

	c.JSON(http.StatusCreated, responseVehicle)
}

// GetUserVehicles gets vehicles for the authenticated user
func (s *SimpleAPI) GetUserVehicles(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var vehicles []domain.Vehicle
	if err := s.DB.Preload("Brand").Preload("Model").
		Where("user_id = ? AND is_deleted = false", userID).
		Order("created_at DESC").Find(&vehicles).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch vehicles"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"vehicles": vehicles})
}

// GetVehicle gets a specific vehicle by ID
func (s *SimpleAPI) GetVehicle(c *gin.Context) {
	vehicleID, err := uuid.Parse(c.Param("vehicle_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vehicle ID"})
		return
	}

	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var vehicle domain.Vehicle
	if err := s.DB.Preload("Brand").Preload("Model").Preload("User").
		Where("id = ? AND is_deleted = false", vehicleID).First(&vehicle).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Vehicle not found"})
		return
	}

	// Check if user owns this vehicle (or is admin)
	userRole, _ := c.Get("user_role")
	if vehicle.UserID.String() != userIDStr.(string) && userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	c.JSON(http.StatusOK, vehicle)
}

// UpdateVehicle updates a vehicle
func (s *SimpleAPI) UpdateVehicle(c *gin.Context) {
	vehicleID, err := uuid.Parse(c.Param("vehicle_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vehicle ID"})
		return
	}

	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req domain.UpdateVehicleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var vehicle domain.Vehicle
	if err := s.DB.Where("id = ? AND is_deleted = false", vehicleID).First(&vehicle).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Vehicle not found"})
		return
	}

	// Check if user owns this vehicle
	if vehicle.UserID.String() != userIDStr.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only update your own vehicles"})
		return
	}

	// Check VIN uniqueness if changing
	if req.VIN != nil && *req.VIN != "" && (vehicle.VIN == nil || *req.VIN != *vehicle.VIN) {
		var existingVehicle domain.Vehicle
		if err := s.DB.Where("vin = ? AND id != ? AND is_deleted = false", *req.VIN, vehicleID).First(&existingVehicle).Error; err == nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Vehicle with this VIN already exists"})
			return
		}
	}

	// Update fields
	if req.Color != nil {
		vehicle.Color = req.Color
	}
	if req.VIN != nil {
		vehicle.VIN = req.VIN
	}
	if req.LicensePlate != nil {
		vehicle.LicensePlate = req.LicensePlate
	}
	if req.Mileage != nil {
		vehicle.Mileage = req.Mileage
	}
	if req.TransmissionType != nil {
		vehicle.TransmissionType = req.TransmissionType
	}
	if req.EngineSize != nil {
		vehicle.EngineSize = req.EngineSize
	}
	if req.FuelType != nil {
		vehicle.FuelType = req.FuelType
	}
	if req.Condition != nil {
		vehicle.Condition = *req.Condition
	}
	if req.PurchaseDate != nil {
		vehicle.PurchaseDate = req.PurchaseDate
	}
	if req.PurchasePrice != nil {
		vehicle.PurchasePrice = req.PurchasePrice
	}
	if req.CurrentValue != nil {
		vehicle.CurrentValue = req.CurrentValue
	}
	if req.InsuranceExpiry != nil {
		vehicle.InsuranceExpiry = req.InsuranceExpiry
	}
	if req.MaintenanceDate != nil {
		vehicle.MaintenanceDate = req.MaintenanceDate
	}
	if req.Notes != nil {
		vehicle.Notes = req.Notes
	}
	if req.ImageURLs != nil {
		vehicle.ImageURLs = req.ImageURLs
	}

	if err := s.DB.Save(&vehicle).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update vehicle"})
		return
	}

	// Load updated vehicle with relations
	var updatedVehicle domain.Vehicle
	if err := s.DB.Preload("Brand").Preload("Model").Where("id = ?", vehicleID).First(&updatedVehicle).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load updated vehicle"})
		return
	}

	c.JSON(http.StatusOK, updatedVehicle)
}

// DeleteVehicle soft deletes a vehicle
func (s *SimpleAPI) DeleteVehicle(c *gin.Context) {
	vehicleID, err := uuid.Parse(c.Param("vehicle_id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vehicle ID"})
		return
	}

	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var vehicle domain.Vehicle
	if err := s.DB.Where("id = ? AND is_deleted = false", vehicleID).First(&vehicle).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Vehicle not found"})
		return
	}

	// Check if user owns this vehicle
	if vehicle.UserID.String() != userIDStr.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only delete your own vehicles"})
		return
	}

	// Soft delete
	vehicle.IsDeleted = true
	if err := s.DB.Save(&vehicle).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete vehicle"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Vehicle deleted successfully",
	})
}

// GetCities returns a list of cities for seller registration
// GetSubscriptionPlans retrieves all active subscription plans.
// @Summary Get all subscription plans
// @Description Get a list of all available subscription plans for sellers.
// @Tags Public
// @Accept json
// @Produce json
// @Success 200 {object} []domainModels.SubscriptionPlan
// @Router /public/subscription-plans [get]
func (s *SimpleAPI) GetSubscriptionPlans(c *gin.Context) {
	// Check if database is available
	if s.DB == nil {
		log.Printf("⚠️ Database connection is nil, returning fallback subscription plans")
		// Return fallback subscription plans for development
		fallbackPlans := []map[string]interface{}{
			{"id": 1, "name": "Basic Plan", "price_monthly": 99.99, "features": []string{"Basic features"}, "is_active": true},
			{"id": 2, "name": "Premium Plan", "price_monthly": 199.99, "features": []string{"Premium features"}, "is_active": true},
		}
		c.JSON(http.StatusOK, fallbackPlans)
		return
	}

	// Completely bypass GORM and use raw SQL with underlying database connection
	// to avoid persistent prepared statement caching issues
	sqlDB, err := s.DB.DB()
	if err != nil {
		log.Printf("❌ Failed to get underlying SQL DB: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to access database"})
		return
	}

	// Use direct SQL query with unique identifier to avoid prepared statement caching
	// Add a comment with timestamp to make each query unique
	query := fmt.Sprintf(`
		-- Query timestamp: %d
		SELECT id, name, description, price_monthly, price_yearly, 
		       features, is_active, created_at, updated_at
		FROM subscription_plans 
		WHERE is_active = true 
		ORDER BY price_monthly ASC
	`, time.Now().UnixNano())

	rows, err := sqlDB.Query(query)
	if err != nil {
		log.Printf("❌ Failed to fetch subscription plans: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription plans"})
		return
	}
	defer rows.Close()

	// Convert rows to map format for JSON response
	var plans []map[string]interface{}
	for rows.Next() {
		var id int
		var name string
		var description, features *string
		var priceMonthly float64
		var priceYearly *float64
		var isActive bool
		var createdAt, updatedAt *time.Time

		err := rows.Scan(&id, &name, &description, &priceMonthly, &priceYearly,
			&features, &isActive, &createdAt, &updatedAt)
		if err != nil {
			log.Printf("❌ Error scanning subscription plan row: %v", err)
			continue // Skip invalid rows
		}

		plan := map[string]interface{}{
			"id":            id,
			"name":          name,
			"price_monthly": priceMonthly,
			"price_yearly":  priceYearly,
			"currency":      "LYD",
			"currency_symbol": "د.ل",
			"currency_name_ar": "دينار ليبي",
			"currency_name_en": "Libyan Dinar",
			"is_active":     isActive,
		}

		if description != nil {
			plan["description"] = *description
		}
		if priceYearly != nil {
			plan["price_yearly"] = *priceYearly
		}
		if features != nil {
			plan["features"] = *features
		}
		if createdAt != nil {
			plan["created_at"] = *createdAt
		}
		if updatedAt != nil {
			plan["updated_at"] = *updatedAt
		}

		plans = append(plans, plan)
	}

	if err = rows.Err(); err != nil {
		log.Printf("❌ Error reading subscription plans data: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Error reading subscription plans data",
		})
		return
	}

	log.Printf("✅ Successfully fetched %d subscription plans using GORM session (no prepared statements)", len(plans))
	c.JSON(http.StatusOK, plans)
}

func (s *SimpleAPI) GetCities(c *gin.Context) {
	// Check if database is available
	if s.DB == nil {
		log.Printf("⚠️ Database connection is nil, returning fallback cities data")
		// Return fallback cities data for development
		fallbackCities := []map[string]interface{}{
			{"id": 1, "name_arabic": "الرياض", "name_english": "Riyadh", "region": "Central", "population": 7000000, "has_major_market": true, "postal_code": "11564"},
			{"id": 2, "name_arabic": "جدة", "name_english": "Jeddah", "region": "Western", "population": 4700000, "has_major_market": true, "postal_code": "21589"},
			{"id": 3, "name_arabic": "مكة المكرمة", "name_english": "Mecca", "region": "Western", "population": 2400000, "has_major_market": true, "postal_code": "24231"},
			{"id": 4, "name_arabic": "المدينة المنورة", "name_english": "Medina", "region": "Western", "population": 1500000, "has_major_market": true, "postal_code": "42311"},
			{"id": 5, "name_arabic": "الدمام", "name_english": "Dammam", "region": "Eastern", "population": 1300000, "has_major_market": true, "postal_code": "32253"},
		}
		c.JSON(http.StatusOK, fallbackCities)
		return
	}

	// Use GORM with Session to completely disable prepared statements
	var cities []map[string]interface{}
	
	// Create a new session with prepared statements explicitly disabled
	session := s.DB.Session(&gorm.Session{
		PrepareStmt: false,
		SkipDefaultTransaction: true,
	})
	
	// Use Find with a struct to avoid raw SQL prepared statement issues
	type City struct {
		ID             int       `gorm:"column:id"`
		NameArabic     *string   `gorm:"column:name_arabic"`
		NameEnglish    *string   `gorm:"column:name_english"`
		Region         *string   `gorm:"column:region"`
		Population     *int      `gorm:"column:population"`
		HasMajorMarket *bool     `gorm:"column:has_major_market"`
		PostalCode     *string   `gorm:"column:postal_code"`
		CreatedAt      *time.Time `gorm:"column:created_at"`
	}
	
	var cityRecords []City
	err := session.Table("public.cities").Order("name_arabic ASC").Find(&cityRecords).Error
	
	if err != nil {
		log.Printf("❌ Failed to fetch cities: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch cities from database",
		})
		return
	}

	// Convert struct records to map format for JSON response
	for _, cityRecord := range cityRecords {
		city := map[string]interface{}{
			"id": cityRecord.ID,
		}

		if cityRecord.NameArabic != nil {
			city["name_arabic"] = *cityRecord.NameArabic
		}
		if cityRecord.NameEnglish != nil {
			city["name_english"] = *cityRecord.NameEnglish
		}
		if cityRecord.Region != nil {
			city["region"] = *cityRecord.Region
		}
		if cityRecord.Population != nil {
			city["population"] = *cityRecord.Population
		}
		if cityRecord.HasMajorMarket != nil {
			city["has_major_market"] = *cityRecord.HasMajorMarket
		}
		if cityRecord.PostalCode != nil {
			city["postal_code"] = *cityRecord.PostalCode
		}
		if cityRecord.CreatedAt != nil {
			city["created_at"] = *cityRecord.CreatedAt
		}

		cities = append(cities, city)
	}

	log.Printf("✅ Successfully fetched %d cities using GORM session (no prepared statements)", len(cities))
	c.JSON(http.StatusOK, cities)
}
