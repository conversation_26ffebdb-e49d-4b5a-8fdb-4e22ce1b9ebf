package services

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// NotificationService handles email and SMS notifications following Forever Plan architecture
type NotificationService struct {
	db     *pgxpool.Pool
	logger *zap.Logger
	config *NotificationConfig
}

// NotificationConfig contains notification service configuration
type NotificationConfig struct {
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	FromEmail    string
	FromName     string
	SMSProvider  string
	SMSAPIKey    string
}

// EmailNotification represents an email notification
type EmailNotification struct {
	To      string
	Subject string
	Body    string
	IsHTML  bool
}

// SMSNotification represents an SMS notification
type SMSNotification struct {
	To      string
	Message string
}

// NewNotificationService creates a new notification service instance
func NewNotificationService(db *pgxpool.Pool, config *NotificationConfig, logger *zap.Logger) *NotificationService {
	return &NotificationService{
		db:     db,
		logger: logger,
		config: config,
	}
}

// SendOrderConfirmationEmail sends order confirmation email to customer
func (s *NotificationService) SendOrderConfirmationEmail(ctx context.Context, userID string, orderID int, orderTotal float64) error {
	s.logger.Info("Sending order confirmation email",
		zap.String("user_id", userID),
		zap.Int("order_id", orderID))

	// Get user email from database
	userEmail, userName, err := s.getUserEmailAndName(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user email: %w", err)
	}

	// Create email content
	subject := fmt.Sprintf("تأكيد الطلب #%d - CarNow", orderID)
	body := s.generateOrderConfirmationEmailBody(userName, orderID, orderTotal)

	// Send email
	notification := &EmailNotification{
		To:      userEmail,
		Subject: subject,
		Body:    body,
		IsHTML:  true,
	}

	err = s.sendEmail(ctx, notification)
	if err != nil {
		s.logger.Error("Failed to send order confirmation email", zap.Error(err))
		return fmt.Errorf("failed to send email: %w", err)
	}

	// Log notification in database
	err = s.logNotification(ctx, userID, "email", "order_confirmation", fmt.Sprintf("Order #%d confirmation", orderID))
	if err != nil {
		s.logger.Warn("Failed to log email notification", zap.Error(err))
		// Don't fail the operation for logging issues
	}

	s.logger.Info("Order confirmation email sent successfully",
		zap.String("user_id", userID),
		zap.Int("order_id", orderID))

	return nil
}

// SendOrderStatusUpdateEmail sends order status update email
func (s *NotificationService) SendOrderStatusUpdateEmail(ctx context.Context, userID string, orderID int, newStatus string) error {
	s.logger.Info("Sending order status update email",
		zap.String("user_id", userID),
		zap.Int("order_id", orderID),
		zap.String("new_status", newStatus))

	// Get user email from database
	userEmail, userName, err := s.getUserEmailAndName(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user email: %w", err)
	}

	// Create email content
	subject := fmt.Sprintf("تحديث حالة الطلب #%d - CarNow", orderID)
	body := s.generateOrderStatusUpdateEmailBody(userName, orderID, newStatus)

	// Send email
	notification := &EmailNotification{
		To:      userEmail,
		Subject: subject,
		Body:    body,
		IsHTML:  true,
	}

	err = s.sendEmail(ctx, notification)
	if err != nil {
		s.logger.Error("Failed to send order status update email", zap.Error(err))
		return fmt.Errorf("failed to send email: %w", err)
	}

	// Log notification in database
	err = s.logNotification(ctx, userID, "email", "order_status_update", fmt.Sprintf("Order #%d status: %s", orderID, newStatus))
	if err != nil {
		s.logger.Warn("Failed to log email notification", zap.Error(err))
	}

	s.logger.Info("Order status update email sent successfully")

	return nil
}

// SendLowStockAlert sends low stock alert to admin
func (s *NotificationService) SendLowStockAlert(ctx context.Context, productID string, productName string, currentStock int) error {
	s.logger.Info("Sending low stock alert",
		zap.String("product_id", productID),
		zap.String("product_name", productName),
		zap.Int("current_stock", currentStock))

	// For now, just log the alert
	// In production, this would send email to admin
	s.logger.Warn("Low stock alert",
		zap.String("product_id", productID),
		zap.String("product_name", productName),
		zap.Int("current_stock", currentStock))

	// Log notification in database
	err := s.logNotification(ctx, "system", "alert", "low_stock", fmt.Sprintf("Product %s low stock: %d", productName, currentStock))
	if err != nil {
		s.logger.Warn("Failed to log low stock alert", zap.Error(err))
	}

	return nil
}

// getUserEmailAndName gets user email and name from database
func (s *NotificationService) getUserEmailAndName(ctx context.Context, userID string) (string, string, error) {
	query := `
		SELECT email, COALESCE(full_name, email) as name
		FROM auth.users 
		WHERE id = $1
	`

	var email, name string
	err := s.db.QueryRow(ctx, query, userID).Scan(&email, &name)
	if err != nil {
		return "", "", fmt.Errorf("failed to get user info: %w", err)
	}

	return email, name, nil
}

// sendEmail sends an email notification (mock implementation)
func (s *NotificationService) sendEmail(_ context.Context, notification *EmailNotification) error {
	// For now, this is a mock implementation
	// In production, this would integrate with SMTP server or email service
	s.logger.Info("Mock email sent",
		zap.String("to", notification.To),
		zap.String("subject", notification.Subject))

	return nil
}

// logNotification logs notification in database using existing table structure
func (s *NotificationService) logNotification(ctx context.Context, userID, notificationType, category, message string) error {
	query := `
		INSERT INTO public.notifications (
			user_id, title, message, type, related_entity_type,
			is_read, is_deleted, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, false, false, NOW(), NOW())
	`

	title := fmt.Sprintf("إشعار %s", category)
	_, err := s.db.Exec(ctx, query, userID, title, message, notificationType, category)
	return err
}

// generateOrderConfirmationEmailBody generates HTML email body for order confirmation
func (s *NotificationService) generateOrderConfirmationEmailBody(userName string, orderID int, orderTotal float64) string {
	return fmt.Sprintf(`
		<html>
		<body dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>مرحباً %s،</h2>
			<p>شكراً لك على طلبك من CarNow!</p>
			<p><strong>رقم الطلب:</strong> #%d</p>
			<p><strong>المبلغ الإجمالي:</strong> %.3f د.ل</p>
			<p>سيتم معالجة طلبك وشحنه في أقرب وقت ممكن.</p>
			<p>يمكنك متابعة حالة طلبك من خلال التطبيق.</p>
			<br>
			<p>شكراً لاختيارك CarNow</p>
			<p>فريق CarNow</p>
		</body>
		</html>
	`, userName, orderID, orderTotal)
}

// generateOrderStatusUpdateEmailBody generates HTML email body for order status update
func (s *NotificationService) generateOrderStatusUpdateEmailBody(userName string, orderID int, newStatus string) string {
	statusText := map[string]string{
		"confirmed":  "تم تأكيد الطلب",
		"processing": "جاري المعالجة",
		"shipped":    "تم الشحن",
		"delivered":  "تم التسليم",
		"cancelled":  "تم الإلغاء",
	}

	status, exists := statusText[newStatus]
	if !exists {
		status = newStatus
	}

	return fmt.Sprintf(`
		<html>
		<body dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>مرحباً %s،</h2>
			<p>تم تحديث حالة طلبك #%d</p>
			<p><strong>الحالة الجديدة:</strong> %s</p>
			<p>يمكنك متابعة تفاصيل طلبك من خلال التطبيق.</p>
			<br>
			<p>شكراً لاختيارك CarNow</p>
			<p>فريق CarNow</p>
		</body>
		</html>
	`, userName, orderID, status)
}
